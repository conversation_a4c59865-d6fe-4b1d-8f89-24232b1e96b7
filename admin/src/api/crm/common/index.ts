import {createCollection, removeCollection} from '@/api/crm/collection'
import request from '@/config/axios'
import {ColumnTypeVo, ColumnVo} from '@/components/Zeadoor/interface'
import { getCustomerList, getCustomerPage } from '@/api/crm/customer'

export enum CrmModelTypeEnum {
    CLUE = 'crmClue',
    CUSTOMER = 'crmCustomer',
    CONTACT = 'crmContact',
    PRODUCT = 'crmProduct',
    CONTRACT = 'crmContract',
    RECEIVABLE = 'crmReceivable',
    RECEIVABLEPLAN = 'crmReceivablePlan',
    INVOICE = 'crmInvoice',
    VISIT = 'crmVisit',
    BUSINESS = 'crmBusiness'
}

const message = useMessage() // 消息弹窗

export const updateCollection = async (
    targetId: number,
    targetType: string,
    collectionId: number
) => {
    return new Promise(async (resolve, reject) => {
        try {
            const params: any = {
                targetType: targetType,
                targetId: targetId
            }
            if (collectionId) {
                await removeCollection(params)
                message.success('取消关注成功!')
                resolve(true)
            } else {
                await createCollection(params)
                message.success('关注成功!')
                resolve(true)
            }
        } catch {
            reject()
        }
    })
}

export const addDetailCommonColumns = (columns: ColumnVo[]) => {
    // 如果第一个字段不是分割线，默认加基本信息
    if (columns[0]?.type !== ColumnTypeVo.divider) columns.splice(0, 0, {label: '基本信息', type: ColumnTypeVo.divider})
    columns.push({label: '系统信息', type: ColumnTypeVo.divider})
    columns.push({label: '负责人', prop: 'leaderName'})
    columns.push({label: '最后跟进时间', prop: 'lastFollowTime'})
    columns.push({label: '创建人', prop: 'creatorName'})
    columns.push({label: '创建时间', prop: 'createTime'})
    columns.push({label: '更新人', prop: 'updaterName'})
    columns.push({label: '更新时间', prop: 'updateTime'})
}

// 获取查重数据API
export const getDuplicateCheckPage = async (params: any) => {
    return await request.get({url: '/crm/common/duplicate/page', params})
}


// 获取客户选择列表
export const customerSelectList = ref([])
export const getCustomerSelectList = async (searchKey?: string) => {
    try {
        const data = await getCustomerPage({
            name: searchKey
        })
        customerSelectList.value = data.list
    } finally {
    }
}
