<template>
  <div class="form-title">完善企业信息</div>
<!--  <div class="form-info"-->
<!--    >先不完善？<span class="is-link" @click="handleRedirect">直接跳过</span></div-->
<!--  >-->
  <div class="form-center">
    <el-form
      ref="formRegisterRef"
      :model="formData"
      :rules="formRules"
      class="login-form"
      label-position="top"
      label-width="100px"
      size="large"
    >
      <el-row style="margin-right: -10px; margin-left: -10px">
        <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
          <el-form-item prop="companyName" label="企业名称">
            <el-input v-model="formData.companyName" placeholder="请输入企业名称" />
          </el-form-item>
        </el-col>
        <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
          <el-form-item prop="companySize" label="企业规模">
            <el-select v-model="formData.companySize" placeholder="请选择企业规模">
              <el-option
                :label="dict.label"
                :value="dict.value"
                v-for="dict in getIntDictOptions(DICT_TYPE.TEAMIFY_COMPANY_SIZE)"
                :key="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
          <el-form-item prop="industryId" label="所属行业">
            <el-select v-model="formData.industryId" placeholder="请选择所属行业">
              <el-option
                :label="dict.label"
                :value="dict.value"
                v-for="dict in getIntDictOptions(DICT_TYPE.TEAMIFY_COMPANY_INDUSTRY)"
                :key="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
          <el-form-item prop="legalMobile" label="联系方式">
            <el-input v-model="formData.legalMobile" placeholder="请输入联系方式" />
          </el-form-item>
        </el-col>
        <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
          <el-form-item prop="areaId" label="所在地区">
            <el-cascader
              v-model="formData.areaId"
              :options="areaList"
              :props="defaultProps"
              class="w-1/1"
              clearable
              filterable
              placeholder="请选择城市"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24" style="padding-right: 10px; padding-left: 10px; margin-top: 10px">
          <el-form-item>
            <XButton
              :loading="loginLoading"
              title="立即完善"
              class="w-[100%]"
              type="primary"
              @click="handleConfirm"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import * as TenantApi from '@/api/tenant/index'
import { useFormValid } from '@/views/Login/components/useLogin'
import { defaultProps } from '@/utils/tree'
import { usePermissionStore } from '@/store/modules/permission'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as AreaApi from '@/api/system/area'

const formRegisterRef = ref()
const { validForm } = useFormValid(formRegisterRef)
const loginLoading = ref(false)
const areaList = ref([])
const { push } = useRouter()
const redirect = ref<string>('')
const permissionStore = usePermissionStore()

const formRules = {
  companyName: [{ required: true, message: '请输入企业名称', trigger: ['blur', 'change'] }],
  companySize: [{ required: true, message: '请选择企业规模', trigger: ['blur', 'change'] }],
  industryId: [{ required: true, message: '请选择所属行业', trigger: ['blur', 'change'] }],
  legalMobile: [
    { required: true, message: '请输入联系方式', trigger: ['blur', 'change'] },
    {pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的联系方式', trigger: ['blur', 'change']}
  ],
  areaId: [{ required: true, message: '所在地区', trigger: ['blur', 'change'] }],
  agree: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (!value) {
          callback('请先阅读相关协议')
        }
        callback()
      },
      trigger: ['blur', 'change']
    }
  ]
}
const formData = ref({
  agree: false,
  companyName: null,
  companySize: null,
  industryId: null,
  areaId: null,
  creditCode: null,
  creditMobile: null,
  businessLicense: null,
  legalPersonName: null,
  legalIdNo: null,
  legalMobile: null
})

/**
 * 确认注册
 */
const handleConfirm = async () => {
  loginLoading.value = true
  try {
    const data = await validForm()
    if (!data) {
      return
    }
    const res = await TenantApi.createCompany(formData.value)
    if (!res) {
      return
    }
    handleRedirect()
  } finally {
    loginLoading.value = false
  }
}

const handleRedirect = async function () {
  // if (!redirect.value) {
  //   redirect.value = '/home'
  // }
  // if (redirect.value.indexOf('sso') !== -1) {
  //   window.location.href = window.location.href.replace('/login?redirect=', '')
  // } else {
  //   await push({ path: redirect.value || permissionStore.addRouters[0].path })
  // }
  window.location.reload() // 重新加载当前页面
}

onMounted(async () => {
  areaList.value = await AreaApi.getAreaTree()
})
</script>

<style scoped lang="scss">
:deep(.mask) {
  height: 100%;
}
</style>
