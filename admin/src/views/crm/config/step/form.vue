<template>
  <el-dialog :title="formData.id ? '编辑阶段流程' : '新增阶段流程'" v-model="dialogVisible" width="1000px" :close-on-click-modal="false" destroy-on-close>
    <el-form ref="formRef" v-loading="formLoading" :model="formData" label-position="top" :rules="formRules" label-width="100px">
      <el-row :gutter="40">
        <div class="form-split">基本信息</div>
        <el-col :span="12">
          <el-form-item label="流程名称" prop="name" required>
            <el-input v-model="formData.name" placeholder="请输入流程名称" maxlength="50" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联对象" prop="targetType" required>
            <el-select v-model="formData.targetType" placeholder="请选择关联对象" @change="handleTargetTypeChange"  remote :remote-method="getStrDictOptions">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.CRM_MODULE_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="适用范围 （默认全公司）" prop="useRangeIdList">
            <ZUserInput v-model="formData.useRangeIdList" :is-multiple="true" :is-init-search="true"/>
          </el-form-item>
        </el-col>

        <div class="form-split">阶段设置</div>
        <el-col :span="24">
          <el-table :data="formData.stepItemList" border style="width: 100%">
            <el-table-column label="阶段" prop="step" width="100px">
              <template #default="scope">
                {{ scope.row.step || '阶段' + (scope.$index + 1) }}
              </template>
            </el-table-column>
            <el-table-column label="阶段名称" prop="name" width="180px">
              <template #default="scope">
                <el-input v-model="scope.row.name" />
              </template>
            </el-table-column>
            <el-table-column label="赢单率" v-if="formData.targetType === CrmModelTypeEnum.BUSINESS" prop="rate" width="200px">
              <template #default="scope">
                <el-input-number v-model="scope.row.rate" style="width: 150px" />
              </template>
            </el-table-column>
            <el-table-column label="阶段工作">
              <template #default="scope">
                <el-button v-if="!scope.row.isEnd" type="primary" @click="openStepWorkItem(scope.row)">
                  编辑阶段工作
                </el-button>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120px">
              <template #default="scope">
                <div class="btn-tool" v-if="!scope.row.isEnd">
                  <el-button type="primary" circle size="small" @click="addStep(scope.$index)">
                    <el-icon>
                      <Plus />
                    </el-icon>
                  </el-button>
                  <el-button type="danger" circle size="small" v-if="scope.$index > 0" @click="removeStep(scope.$index)">
                    <el-icon>
                      <Delete />
                    </el-icon>
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm" :disabled="formLoading">确 定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 阶段工作项编辑组件 -->
  <StepWorkItem ref="stepWorkItemRef" @success="handleStepWorkItemSuccess" />
</template>

<script lang="ts" setup>
import * as StepApi from '@/api/crm/step/index'
import { Delete, Plus } from '@element-plus/icons-vue'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import StepWorkItem from '@/views/crm/common/step/step-work-item.vue'
import { CrmModelTypeEnum } from '@/api/crm/common'

// 弹窗控制
const dialogVisible = ref(false)
const formLoading = ref(false)
const formRef = ref()
const stepWorkItemRef = ref()
const currentEditingStep = ref<any>(null)

const message = useMessage()

// 表单数据
const formData: any = ref({
  id: null,
  name: '',
  targetType: '',
  isEffect: true,
  useRangeIdList: [],
  stepItemList: []
})

// 表单校验规则
const formRules = reactive({
  name: [{ required: true, message: '流程名称不能为空', trigger: 'blur' }],
  targetType: [{ required: true, message: '关联对象不能为空', trigger: 'change' }]
})

// 打开弹窗
const open = async (id?: number) => {
  dialogVisible.value = true
  resetForm()

  // 如果有ID，查询详情
  if (id) {
    formLoading.value = true
    try {
      const data = await StepApi.getStep(id)
      formData.value = data
    } finally {
      formLoading.value = false
    }
  } else {
    // 初始化默认阶段
    initDefaultSteps()
  }
}

// 初始化默认阶段
const initDefaultSteps = () => {
  formData.value.stepItemList = [
    {
      name: '阶段1',
      taskList: [],
      rate: 0
    },
    {
      step: '结束-成功',
      name: '成功',
      taskList: [],
      rate: 0,
      isEnd: true,
      status: 1
    },
    {
      step: '结束-失败',
      name: '失败',
      taskList: [],
      rate: 0,
      isEnd: true,
      status: 2
    }
  ]
}

// 处理关联对象类型变更
const handleTargetTypeChange = (val: string) => {
  // 清除所有结束阶段
  formData.value.stepItemList = formData.value.stepItemList.filter((item: any) => !item.isEnd)

  // 根据关联对象类型添加不同的结束阶段
  if (val === CrmModelTypeEnum.BUSINESS) {
    formData.value.stepItemList.push({
      step: '结束',
      name: '赢单',
      taskList: [],
      rate: 100,
      isEnd: true,
      status: 1
    })
    formData.value.stepItemList.push({
      step: '结束',
      name: '输单',
      taskList: [],
      rate: 0,
      isEnd: true,
      status: 2
    })
    formData.value.stepItemList.push({
      step: '结束',
      name: '无效',
      taskList: [],
      rate: 0,
      isEnd: true,
      status: 3
    })
  } else {
    formData.value.stepItemList.push({
      step: '结束-成功',
      name: '成功',
      taskList: [],
      rate: 0,
      isEnd: true,
      status: 1
    })
    formData.value.stepItemList.push({
      step: '结束-失败',
      name: '失败',
      taskList: [],
      rate: 0,
      isEnd: true,
      status: 2
    })
  }
}

// 添加阶段
const addStep = (index: number) => {
  formData.value.stepItemList.splice(index + 1, 0, {
    name: '',
    taskList: [],
    rate: 0
  })
}

// 删除阶段
const removeStep = (index: number) => {
  formData.value.stepItemList.splice(index, 1)
}

// 打开阶段工作项编辑
const openStepWorkItem = (step: any) => {
  currentEditingStep.value = step
  stepWorkItemRef.value.open({
    dataList: step.taskList || [],
    success: handleStepWorkItemSuccess
  })
}

// 处理阶段工作项编辑成功
const handleStepWorkItemSuccess = (dataList: any[]) => {
  if (currentEditingStep.value) {
    currentEditingStep.value.taskList = dataList
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    id: null,
    name: '',
    targetType: '',
    isEffect: true,
    useRangeIdList: [],
    stepItemList: []
  }
  formRef.value?.resetFields()
}

// 提交表单
const emit = defineEmits(['complete'])
const submitForm = async () => {
  // 表单验证
  if (!formRef.value) return

  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return

  // 验证阶段名称
  if (formData.value.stepItemList.some((item: any) => !item.name)) {
    message.error('阶段名称不能为空')
    return
  }

  // 验证商机类型的赢单率
  if (formData.value.targetType === 'business' && formData.value.stepItemList.some((item: any) => !item.isEnd && item.rate === undefined)) {
    message.error('赢单率不能为空')
    return
  }

  // 提交数据
  formLoading.value = true
  try {
    if (!formData.value.id) {
      await StepApi.createStep(formData.value)
      message.success('创建成功')
    } else {
      await StepApi.updateStep(formData.value)
      message.success('更新成功')
    }
    dialogVisible.value = false
    emit('complete')
  } finally {
    formLoading.value = false
  }
}

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped lang="scss">
.form-split {
  width: 100%;
  font-size: 16px;
  font-weight: 500;
  margin: 10px 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.btn-tool {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
