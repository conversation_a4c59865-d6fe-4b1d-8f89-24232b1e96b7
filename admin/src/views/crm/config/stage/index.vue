<template>
  <div class="zeadoor-list-container custom-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>阶段管理</h2>
        <p>管理线索、客户、商机的阶段</p>
      </div>
    </div>
    <div class="page-center">
      <!-- 标签类型选择 -->
      <div class="header-panel">
        <el-tabs v-model="activeStageType" @tab-change="onStageTypeChange" class="type-tabs">
          <el-tab-pane :label="'线索阶段'" :name="CrmModelTypeEnum.CLUE">
            <template #label>
              <div class="tab-label">
                <el-icon>
                  <Document />
                </el-icon>
                <span>线索阶段</span>
              </div>
            </template>
          </el-tab-pane>
          <el-tab-pane :label="'客户阶段'" :name="CrmModelTypeEnum.CUSTOMER">
            <template #label>
              <div class="tab-label">
                <el-icon>
                  <User />
                </el-icon>
                <span>客户阶段</span>
              </div>
            </template>
          </el-tab-pane>
          <el-tab-pane :label="'商机阶段'" :name="CrmModelTypeEnum.BUSINESS">
            <template #label>
              <div class="tab-label">
                <el-icon>
                  <TrendCharts />
                </el-icon>
                <span>商机阶段</span>
              </div>
            </template>
          </el-tab-pane>
        </el-tabs>
      </div>
      <!-- 主要内容区域 -->
      <div class="main-panel">
        <ZCustomTable ref="tableRef" :is-custom="false" :config="config" @get-list="getList" @delete="handleDelete">
          <template #column-prop-stageName="scope">
            {{ scope.row.stageName }}
          </template>
        </ZCustomTable>
      </div>
    </div>
    <!-- 阶段表单弹窗 -->
    <StageForm ref="stageFormRef" @success="refreshList" />
  </div>
</template>

<script lang="ts" setup>
import {Document, TrendCharts, User} from '@element-plus/icons-vue'
import * as StageApi from '@/api/crm/stage'
import { CrmModelTypeEnum } from '@/api/crm/common'
import StageForm from './form.vue'
import { merge } from 'lodash-es'
import {SearchToolAddButton, TableToolDeleteButton, TableToolUpdateButton} from '@/components/Zeadoor/CustomTable/table'
import {ColumnTypeVo} from "@/components/Zeadoor/interface";
import {DICT_TYPE} from "@/utils/dict";

defineOptions({ name: 'CrmStageManagement' })

const activeStageType = ref<any>(CrmModelTypeEnum.CLUE)
const stageFormRef = ref()
const tableRef = ref()

// 表格配置
const config = ref<any>({
  table: {
    showSelection: false,
    columns: [
      {
        label: '阶段名称',
        prop: 'stageName',
        minWidth: 120
      },
      {
        label: '阶段类型',
        prop: 'stageType',
        type: ColumnTypeVo.dict,
        dictKey: DICT_TYPE.CRM_STAGE_TYPE,
        minWidth: 120
      },
      {
        label: '创建时间',
        prop: 'createTime',
        width: 180
      },
      {
        label: '更新时间',
        prop: 'updateTime',
        width: 180
      }
    ],
    toolbar: {
      width: 200,
      buttons: [
        merge({}, TableToolUpdateButton, {
          vIf: (scope: any) => {
            return scope.row.isSystem === false
          },
          // hasPermission: ['crm:clue:delete'],
          click: (scope: any) => {
            stageFormRef.value.open('update', scope.row.targetType, scope.row)
          }
        }),
        merge({}, TableToolDeleteButton, {
          vIf: (scope: any) => {
            return scope.row.isSystem === false
          }
        })
      ]
    }
  },
  search: {
    show: true,
    showMore:false,
    toolbar: {
      buttons: [
        merge({}, SearchToolAddButton, {
          click: () => {
            handleAdd()
          }
        })
      ]
    },
  }
})

// 切换阶段类型
const onStageTypeChange = (type: string) => {
  activeStageType.value = type
  // 确保表格刷新
  nextTick(() => {
    tableRef.value?.reload()
  })
}

// 获取阶段列表
const getList = async (queryParams: any, done: Function, error: Function) => {
  queryParams.targetType = activeStageType.value
  try {
    const data = await StageApi.getStagePage(queryParams)
    done(data.list, data.total)
  } catch {
    error()
  }
}

// 刷新列表
const refreshList = () => {
  tableRef.value.reload()
}

// 新增阶段
const handleAdd = () => {
  stageFormRef.value.open('create', activeStageType.value)
}

/** 删除按钮操作 */
const handleDelete = async (ids: number[], done: Function, error: Function) => {
  try {
    await StageApi.deleteStage(ids)
    done()
  } catch {
    error()
  }
}
</script>

<style lang="scss" scoped>
.stage-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: #ffffff;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .header-content {
    h2 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }
}

.stage-type-section {
  background: #ffffff;
  border-radius: 8px;
  padding: 20px 24px 0;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .type-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 0;
    }
  }
}

.action-bar {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
}

.stage-list {
  background: #ffffff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}
</style>
