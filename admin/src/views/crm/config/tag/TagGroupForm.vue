<template>
  <el-dialog v-model="dialogVisible" :title="formTitle" width="500px" :close-on-click-modal="false">
    <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px" @submit.prevent>
      <el-form-item label="分组名称" prop="groupName">
        <el-input v-model="formData.groupName" placeholder="请输入分组名称" maxlength="32" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm" :loading="formLoading">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import * as TagGroupApi from '@/api/crm/tag/group/index'

defineOptions({ name: 'TagGroupForm' })

const emit = defineEmits(['success'])
const message = useMessage()

// 弹窗状态
const dialogVisible = ref(false)
const formLoading = ref(false)
const formTitle = ref('')

// 表单引用
const formRef = ref()

// 表单数据
const formData = ref({
  id: undefined,
  groupName: '',
  sort: 1
})

// 当前模块类型
const currentTargetType = ref('')

// 表单验证规则
const formRules = {
  groupName: [
    { required: true, message: '请输入分组名称', trigger: 'blur' },
    { min: 1, max: 32, message: '分组名称长度在 1 到 32 个字符', trigger: 'blur' }
  ]
}

// 打开弹窗
const open = (type: string, targetType: string, record?: any, sort?: number) => {
  dialogVisible.value = true
  formTitle.value = type === 'create' ? '新建分组' : '编辑分组'
  currentTargetType.value = targetType

  if (type === 'create') {
    formData.value = {
      id: undefined,
      groupName: '',
      sort: sort || 1
    }
  } else {
    formData.value = {
      id: record.id,
      groupName: record.groupName,
      sort: record.sort || 1
    }
  }

  // 重置表单验证
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    formLoading.value = true

    // 构建请求参数
    const requestData: any = {
      id: formData.value.id,
      targetType: currentTargetType.value,
      groupName: formData.value.groupName,
      sort: formData.value.sort
    }

    if (formData.value.id) {
      await TagGroupApi.updateTagGroup(requestData)
      message.success('更新分组成功')
    } else {
      await TagGroupApi.createTagGroup(requestData)
      message.success('创建分组成功')
    }

    dialogVisible.value = false
    emit('success')
  } finally {
    formLoading.value = false
  }
}

// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped></style>
