<template>
  <el-dialog v-model="dialogVisible" :title="formTitle" width="500px" :close-on-click-modal="false">
    <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px" @submit.prevent>
      <el-form-item label="标签名称" prop="tagName">
        <el-input v-model="formData.tagName" placeholder="请输入标签名称" maxlength="32" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm" :loading="formLoading">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import * as TagApi from '@/api/crm/tag/index'

defineOptions({ name: 'TagForm' })

const emit = defineEmits(['success'])
const message = useMessage()

// 弹窗状态
const dialogVisible = ref(false)
const formLoading = ref(false)
const formTitle = ref('')

// 表单引用
const formRef = ref()

// 表单数据
const formData = ref({
  id: undefined,
  tagName: '',
  sort: 1
})

// 当前模块类型和分组ID
const currentTargetType = ref('')
const currentGroupId = ref<number>(0)

// 表单验证规则
const formRules = {
  tagName: [
    { required: true, message: '请输入标签名称', trigger: 'blur' },
    { min: 1, max: 32, message: '标签名称长度在 1 到 32 个字符', trigger: 'blur' }
  ]
}

// 打开弹窗
const open = (type: string, targetType: string, groupId: number, record?: any, sort?: number) => {
  dialogVisible.value = true
  formTitle.value = type === 'create' ? '新建标签' : '编辑标签'
  currentTargetType.value = targetType
  currentGroupId.value = groupId

  if (type === 'create') {
    formData.value = {
      id: undefined,
      tagName: '',
      sort: sort || 1
    }
  } else {
    formData.value = {
      id: record.id,
      tagName: record.tagName,
      sort: record.sort || 1
    }
  }

  // 重置表单验证
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    formLoading.value = true

    if (!currentGroupId.value) {
      message.error('分组ID不能为空')
      return
    }

    // 构建请求参数
    const requestData = {
      id: formData.value.id,
      targetType: currentTargetType.value,
      groupId: currentGroupId.value,
      tagName: formData.value.tagName,
      sort: formData.value.sort
    }

    if (formData.value.id) {
      await TagApi.updateTag(requestData)
      message.success('更新标签成功')
    } else {
      await TagApi.createTag(requestData)
      message.success('创建标签成功')
    }

    dialogVisible.value = false
    emit('success')
  } finally {
    formLoading.value = false
  }
}

// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped></style>
