<template>
  <div class="zeadoor-list-container custom-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>标签管理</h2>
        <p>管理线索、客户、商机的标签分组和标签</p>
      </div>
    </div>
    <div class="zeadoor-list-container-main page-center">
      <!-- 标签类型选择 -->
      <div class="header-panel">
        <el-tabs v-model="activeTagType" @tab-change="onTagTypeChange" class="type-tabs">
          <el-tab-pane :label="'线索标签'" :name="CrmModelTypeEnum.CLUE">
            <template #label>
              <div class="tab-label">
                <el-icon><Document /></el-icon>
                <span>线索标签</span>
              </div>
            </template>
          </el-tab-pane>
          <el-tab-pane :label="'客户标签'" :name="CrmModelTypeEnum.CUSTOMER">
            <template #label>
              <div class="tab-label">
                <el-icon><User /></el-icon>
                <span>客户标签</span>
              </div>
            </template>
          </el-tab-pane>
          <el-tab-pane :label="'商机标签'" :name="CrmModelTypeEnum.BUSINESS">
            <template #label>
              <div class="tab-label">
                <el-icon><TrendCharts /></el-icon>
                <span>商机标签</span>
              </div>
            </template>
          </el-tab-pane>
        </el-tabs>
      </div>
      <!-- 主要内容区域 -->
      <div class="main-content" v-loading="loading">
        <div class="content-layout">
          <!-- 左侧：标签分组管理 -->
          <div class="group-panel">
            <div class="panel-header">
              <h3>标签分组</h3>
              <el-button type="primary" @click="onAddTagGroup" :icon="Plus" size="small">
                新建分组
              </el-button>
            </div>

            <div class="group-list">
              <div v-for="group in currentTagGroups" :key="group.id" class="group-card" :class="{ active: selectedGroupId === group.id }" @click="selectGroup(group.id)">
                <div class="group-main">
                  <div class="group-name">{{ group.groupName }}</div>
                  <div class="group-stats">{{ group.tagDOList?.length || 0 }}个标签</div>
                </div>
                <div class="group-actions">
                  <el-dropdown @command="handleGroupAction" trigger="click">
                    <el-button type="text" :icon="MoreFilled" @click.stop />
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item :command="{ action: 'edit', group }" style="color: #409EFF">
                          <el-icon><Edit /></el-icon>编辑
                        </el-dropdown-item>
                        <el-dropdown-item :command="{ action: 'delete', group }" divided style="color: #F56C6C">
                          <el-icon><Delete /></el-icon>删除
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：标签管理 -->
          <div class="tag-panel">
            <div class="panel-header">
              <h3>标签管理</h3>
              <el-button type="primary" @click="onAddTag" :icon="Plus" size="small" :disabled="!selectedGroupId">
                新建标签
              </el-button>
            </div>

            <div class="tag-content">
              <!-- 选中分组的标签 -->
              <div v-if="selectedGroup" class="tag-section">
                <div class="section-info">
                  <h4>{{ selectedGroup.groupName }}</h4>
                  <span class="tag-count">共 {{ selectedGroup.tagDOList?.length || 0 }} 个标签</span>
                </div>

                <div class="tags-container">
                  <div v-for="tag in selectedGroup.tagDOList" :key="tag.id" class="tag-wrapper">
                    <el-tag effect="light" closable @close="onDeleteTag(tag)" @click="onEditTag(tag)" class="custom-tag" size="large">
                      {{ tag.tagName }}
                    </el-tag>
                  </div>

                  <!-- 添加标签按钮 -->
                  <div class="add-tag-btn">
                    <el-button type="primary" plain @click="onAddTag" :icon="Plus" class="add-btn">
                      添加标签
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 分组表单弹窗 -->
  <TagGroupForm ref="groupFormRef" @success="getList" />

  <!-- 标签表单弹窗 -->
  <TagForm ref="tagFormRef" @success="getList" />
</template>

<script lang="ts" setup>
import { Plus, Edit, Delete, Document, User, TrendCharts, MoreFilled } from '@element-plus/icons-vue'
import * as TagGroupApi from '@/api/crm/tag/group'
import * as TagApi from '@/api/crm/tag'
import { CrmModelTypeEnum } from '@/api/crm/common'
import TagGroupForm from './TagGroupForm.vue'
import TagForm from './TagForm.vue'

defineOptions({ name: 'CrmTagManagement' })

// 消息提示
const message = useMessage()

const loading = ref(false)
const activeTagType = ref<any>(CrmModelTypeEnum.CLUE)
const selectedGroupId = ref<any>(null)

// 标签分组数据
const tagGroupList = ref<any[]>([])

// 表单引用
const groupFormRef = ref()
const tagFormRef = ref()

// 当前标签类型的分组列表
const currentTagGroups = computed(() => {
  return tagGroupList.value.filter(group => group.targetType === activeTagType.value)
})

// 当前选中的分组
const selectedGroup = computed(() => {
  return currentTagGroups.value.find(group => group.id === selectedGroupId.value)
})

// 标签类型切换
const onTagTypeChange = (type: string) => {
  activeTagType.value = type
  selectedGroupId.value = null
  getList(type)
}

// 选择分组
const selectGroup = (groupId: number) => {
  selectedGroupId.value = groupId
}

// 处理分组操作（编辑/删除）
const handleGroupAction = ({ action, group }: { action: string, group: any }) => {
  if (action === 'edit') {
    onEditGroup(group)
  } else if (action === 'delete') {
    deleteTagGroup(group.id)
  }
}

// 添加标签分组
const onAddTagGroup = () => {
  const nextSort = currentTagGroups.value.length + 1
  groupFormRef.value?.open('create', activeTagType.value, null, nextSort)
}

// 编辑标签分组
const onEditGroup = (group: any) => {
  groupFormRef.value?.open('update', activeTagType.value, group)
}

const onAddTag = () => {
  if (!selectedGroupId.value) {
    message.warning('请先选择一个分组')
    return
  }
  // 计算当前分组下的标签数量，新标签的sort为数量+1
  const currentGroup = selectedGroup.value
  const nextSort = (currentGroup?.tagDOList?.length || 0) + 1
  tagFormRef.value?.open('create', activeTagType.value, selectedGroupId.value, null, nextSort)
}

const onEditTag = (tag: any) => {
  tagFormRef.value?.open('update', activeTagType.value, selectedGroupId.value, tag)
}

const getList = async (targetType?: string) => {
  loading.value = true
  try {
    const params = targetType ? { targetType: targetType } : {targetType: activeTagType.value}
    const data = await TagGroupApi.getTagGroupList(params)
    tagGroupList.value = data.list
  } finally {
    loading.value = false
  }
}

const deleteTagGroup = async (id: number) => {
  await TagGroupApi.deleteTagGroup(id)
  message.success('删除分组成功')

  await getList()
}

const onDeleteTag = async (tag: any) => {
  await TagApi.deleteTag(tag.id)
  message.success('删除标签成功')

  await getList()
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>

.main-content {
  background: #fff;
  .content-layout {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 20px;
    height: calc(100vh - 280px);
  }
}

.group-panel{
  border-right: 1px solid var(--el-border-color-extra-light);
}

.group-panel, .tag-panel {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  padding: 20px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.group-list {
  flex: 1;
  padding: 16px;
  overflow-y: auto;

  .group-card {
    padding: 16px;
    border: 1px solid #ebeef5;
    border-radius: 6px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &:hover {
      border-color: #409eff;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
    }

    &.active {
      border-color: #409eff;
      background-color: #f0f9ff;
    }

    &:last-child {
      margin-bottom: 0;
    }

    .group-main {
      flex: 1;

      .group-name {
        font-size: 14px;
        font-weight: 500;
        color: #303133;
        margin-bottom: 4px;
      }

      .group-stats {
        font-size: 12px;
        color: #909399;
      }
    }

    .group-actions {
      .el-button {
        padding: 4px;
      }
    }
  }

  .empty-groups {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
  }
}

.tag-content {
  flex: 1;
  padding: 20px 24px;
  overflow-y: auto;

  .tag-section {
    .section-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 12px;
      border-bottom: 1px solid #ebeef5;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .tag-count {
        font-size: 12px;
        color: #909399;
        background: #f5f7fa;
        padding: 4px 8px;
        border-radius: 4px;
      }
    }

    .tags-container {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      align-items: center;

      .tag-wrapper {
        .custom-tag {
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
        }
      }

      .add-tag-btn {
        .add-btn {
          border-style: dashed;
          border-color: #d9d9d9;
          color: #666;

          &:hover {
            border-color: #409eff;
            color: #409eff;
          }
        }
      }
    }
  }

  .no-selection {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .content-layout {
    grid-template-columns: 300px 1fr;
  }
}

@media (max-width: 768px) {
  .tag-management {
    padding: 16px;
  }

  .content-layout {
    grid-template-columns: 1fr;
    height: auto;
  }

  .group-panel {
    height: 300px;
  }

  .tag-panel {
    height: 400px;
  }
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}
</style>
