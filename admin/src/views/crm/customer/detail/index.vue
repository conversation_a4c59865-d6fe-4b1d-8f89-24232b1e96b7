<template>
  <el-drawer class="z-detail-drawer" v-model="dialogVisible" @close="close" size="1500px">
    <template #header>
      <div class="z-detail-drawer-header">
        <div class="title">
          <div class="title-icon">
            <el-icon @click="updateStar(detailInfo)" v-if="detailInfo?.collectionId" size="25" style="color: var(--el-color-warning)">
              <StarFilled/>
            </el-icon>
            <el-icon @click="updateStar(detailInfo)" style="color: #9a9a9a" size="20" v-else>
              <Star/>
            </el-icon>
          </div>
          <div class="title-icon">
            <el-icon v-if="detailInfo?.isLock" size="20" style="color: var(--el-color-danger)">
              <Lock/>
            </el-icon>
            <el-icon style="color: #9a9a9a" size="20" v-else>
              <Unlock/>
            </el-icon>
          </div>
          <div class="title-label">
            {{ detailInfo?.name || '客户详情' }}
          </div>
          <div class="tag">
            <el-tag class="m-l-2 m-r-2" v-if="detailInfo?.status === 10" type="danger">未成交</el-tag>
            <el-tag class="m-l-2 m-r-2" v-else-if="detailInfo?.status === 20" type="success">已成交</el-tag>
          </div>
        </div>
        <div class="tool">
          <el-button @click="handlePutSeas([detailInfo?.id])" v-hasPermi="['crm:customer:put-seas']">放入公海
          </el-button>
          <el-button @click="transferLeaderRef.open({targetIdList: [detailInfo?.id]})"
                     v-hasPermi="['crm:team:transfer-leader']">转移负责人
          </el-button>
          <el-button @click="handleBatchLock([detailInfo?.id], !detailInfo?.isLock)" v-hasPermi="['crm:customer:lock']">
            {{ detailInfo?.isLock ? '解锁' : '锁定' }}
          </el-button>
          <el-button
            @click="handleDeal([detailInfo?.id], detailInfo?.status !== 20)"
            :type="detailInfo?.status !== 20 ? 'success' : 'default'"
            v-hasPermi="['crm:customer:finish']"
          >
            {{ detailInfo?.status !== 20 ? '成交' : '未成交' }}
          </el-button>
          <el-button type="primary" @click="customerFormRef.open(detailInfo?.id)" v-hasPermi="['crm:customer:update']">
            编辑
          </el-button>
          <el-button type="danger" @click="handleDelete([detailInfo?.id])" v-hasPermi="['crm:customer:delete']">删除</el-button>
        </div>
      </div>
    </template>

    <div class="detail-body">
      <div class="detail-main" v-if="detailInfo">
        <div class="detail-main-create">
          <el-button link type="primary" @click="contactFormRef.open()" v-hasPermi="['crm:contact:create']">
            <el-icon>
              <CirclePlusFilled/>
            </el-icon>
            创建联系人
          </el-button>
          <el-button link type="primary" @click="businessFormRef.open()" v-hasPermi="['crm:business:create']">
            <el-icon>
              <CirclePlusFilled/>
            </el-icon>
            创建商机
          </el-button>
          <el-button link type="primary" @click="contractFormRef.open()" v-hasPermi="['crm:contract:create']">
            <el-icon>
              <CirclePlusFilled/>
            </el-icon>
            创建合同
          </el-button>
          <el-button link type="primary" @click="receivableFormRef.open()" v-hasPermi="['crm:receivable:create']">
            <el-icon>
              <CirclePlusFilled/>
            </el-icon>
            创建回款
          </el-button>
        </div>
        <div class="detail-main-relation">
          <el-button @click="teamListRef.open()">团队成员({{ customerGroupCount?.teamCount }})</el-button>
          <el-button @click="contractListRef.open()">合同({{ customerGroupCount?.contractCount }})</el-button>
          <el-button @click="contactListRef.open()">联系人({{ customerGroupCount?.contactsCount }})</el-button>
          <el-button @click="businessListRef.open()">商机({{ customerGroupCount?.businessCount }})</el-button>
          <el-button @click="planListRef.open()">回款计划</el-button>
          <el-button @click="visitListRef.open()">回访</el-button>
          <el-button @click="invoiceListRef.open()">发票</el-button>
          <el-button @click="invoiceHeaderListRef.open()">发票抬头</el-button>
          <el-button @click="customerAddressRef.open()">客户地址</el-button>
          <el-button @click="receivableListRef.open()">回款</el-button>
          <el-button @click="openParentCustomerList">上级客户</el-button>
          <el-button @click="openSubCustomerList">下级客户</el-button>
        </div>
        <div class="detail-main-form">
          <ZCustomDetail page-key="crmCustomer" :is-custom="true" ref="formRef" v-model="detailInfo"
                         :data-id="detailInfo.id" @get-detail="getDetail" @before-set-columns="onBeforeSetColumns">
            <!-- 上级名称 -->
            <template #form-prop-parentId>
              {{ detailInfo.parentName || '-' }}
            </template>
            <!-- 阶段名称 -->
            <template #form-prop-stageId>
              {{ detailInfo.stageName || '-' }}
            </template>
            <!-- 城市 -->
            <template #form-prop-cityCode>
              {{ detailInfo.cityName || '-' }}
            </template>
          </ZCustomDetail>
        </div>
        <div class="step-panel">
          <StepDetailPanel v-if="detailInfo?.id" :target-type="CrmModelTypeEnum.CUSTOMER" :target-id="detailInfo?.id"/>
        </div>
      </div>
      <div class="detail-dashboard">
        <CustomerDetailExpand v-if="detailInfo?.id" :target-type="CrmModelTypeEnum.CUSTOMER" :target-id="detailInfo?.id"/>
      </div>
    </div>
  </el-drawer>

  <template v-if="detailInfo?.name">
    <CustomerForm ref="customerFormRef" @complete="formRef.reload()"/>
    <ContactForm ref="contactFormRef" @complete="onCreateSuccess(detailInfo?.id)" :default-val="defaultVal" :disabled-props="['customerId']"/>
    <ContractForm ref="contractFormRef" @complete="onCreateSuccess(detailInfo?.id)" :default-val="defaultVal" :disabled-props="['customerId']"/>
    <BusinessForm ref="businessFormRef" @complete="onCreateSuccess(detailInfo?.id)" :default-val="defaultVal" :disabled-props="['customerId']"/>
    <ReceivableForm ref="receivableFormRef" :default-val="defaultVal" :disabled-props="['customerId']"/>
    <PowerLeaderTransfer ref="transferLeaderRef" :targetType="CrmModelTypeEnum.CUSTOMER"/>
    <PutSeasDialog ref="putSeasDialogRef" @success="onPutSeasSuccess"/>

    <ContractListDialog v-if="detailInfo?.id"
                        :title="detailInfo?.name"
                        :condition="defaultVal"
                        :default-val="defaultVal"
                        :disabled-props="['customerId']"
                        ref="contractListRef"/>
    <BusinessListDialog v-if="detailInfo?.id"
                        :title="detailInfo?.name"
                        :condition="{customerId: detailInfo?.id}"
                        :default-val="defaultVal"
                        :disabled-props="['customerId']"
                        ref="businessListRef"/>
    <InvoiceListDialog v-if="detailInfo?.id"
                       :title="detailInfo?.name"
                       :condition="{customerId: detailInfo?.id}"
                       :default-val="defaultVal"
                       :disabled-props="['customerId']"
                       ref="invoiceListRef"/>
    <VisitListDialog v-if="detailInfo?.id"
                     :title="detailInfo?.name"
                     :condition="{customerId: detailInfo?.id}"
                     :default-val="defaultVal"
                     :disabled-props="['customerId']"
                     ref="visitListRef"/>
    <PlanListDialog v-if="detailInfo?.id"
                    :title="detailInfo?.name"
                    :condition="{customerId: detailInfo?.id}"
                    :default-val="defaultVal"
                    :disabled-props="['customerId']"
                    ref="planListRef"/>
    <ReceivableList v-if="detailInfo?.id"
                    :title="detailInfo?.name"
                    :condition="{customerId: detailInfo?.id}"
                    :default-val="defaultVal"
                    :disabled-props="['customerId']"
                    ref="receivableListRef"/>
    <ContactListDialog v-if="detailInfo?.id"
                       :title="detailInfo?.name"
                       :condition="{customerId: detailInfo?.id, customerName: detailInfo?.name}"
                       :default-val="defaultVal"
                       :disabled-props="['customerId']"
                       ref="contactListRef"/>
    <TeamListDialog v-if="detailInfo?.id"
                    :title="detailInfo?.name"
                    :targetType="CrmModelTypeEnum.CUSTOMER"
                    :target-id="detailInfo?.id"
                    ref="teamListRef"/>

    <!-- 发票抬头  -->
    <InvoiceHeader v-if="detailInfo?.id" :customer-id="detailInfo?.id" ref="invoiceHeaderListRef"/>

    <!-- 客户地址 -->
    <CustomerCustomerAddress v-if="detailInfo?.id" :customer-id="detailInfo?.id" ref="customerAddressRef"/>

    <!-- 客户关系列表对话框 -->
    <CustomerRelationList v-if="detailInfo?.id" :title="detailInfo?.name" :condition="{customerId: detailInfo?.id}"
                          ref="customerRelationListDialogRef"/>
  </template>
</template>

<script lang="ts" setup>
import * as MainApi from '@/api/crm/customer'
import { batchFail, batchFinish, batchLock, batchUnLock } from '@/api/crm/customer'
import {ColumnVo} from '@/components/Zeadoor/interface'
import {addDetailCommonColumns, CrmModelTypeEnum, updateCollection} from '@/api/crm/common'
import {CirclePlusFilled, Lock, Star, StarFilled, Unlock} from "@element-plus/icons-vue";
import ContactForm from "@/views/crm/contact/form.vue";
import ContractForm from "@/views/crm/contract/form.vue";
import BusinessForm from "@/views/crm/business/form.vue";
import ReceivableForm from "@/views/crm/receivable/form.vue";
import CustomerForm from "@/views/crm/customer/form.vue";
import PowerLeaderTransfer from "@/views/crm/common/transfer/power-leader.vue";
import PutSeasDialog from '@/views/crm/common/seas/put-seas-dialog.vue';
import StepDetailPanel from "@/views/crm/common/step/step-detail.vue";
import ContractListDialog from "@/views/crm/contract/components/contract-dialog.vue";
import BusinessListDialog from "@/views/crm/business/components/business-dialog.vue";
import VisitListDialog from "@/views/crm/visit/components/visit-dialog.vue";
import InvoiceListDialog from "@/views/crm/invoice/components/invoice-dialog.vue";
import PlanListDialog from "@/views/crm/receivable/components/plan-dialog.vue";
import ReceivableList from "@/views/crm/receivable/components/receivable-dialog.vue";
import ContactListDialog from "@/views/crm/contact/components/contact-dialog.vue";
import CustomerRelationList from "@/views/crm/common/customer/customer-relation-list.vue";
import CustomerCustomerAddress from "@/views/crm/common/customer/customer-address.vue";
import InvoiceHeader from "@/views/crm/common/customer/invoice-header.vue";
import TeamListDialog from "@/views/crm/common/team/components/team-dialog.vue";
import CustomerDetailExpand from '@/views/crm/customer/detail/expand.vue'

const detailInfo = ref<any>()
const customerFormRef = ref()
const formRef = ref()
const dialogVisible = ref(false) // 弹窗的是否展示
const contactFormRef = ref()
const contractFormRef = ref()
const businessFormRef = ref()
const receivableFormRef = ref()
const transferLeaderRef = ref()
const putSeasDialogRef = ref()
const activeTab = ref('follow') // 默认选中跟进记录标签
const contractListRef = ref()
const businessListRef = ref()
const invoiceListRef = ref()
const visitListRef = ref()
const planListRef = ref()
const receivableListRef = ref()
const contactListRef = ref()
const teamListRef = ref()
const customerGroupCount = ref()
const customerRelationListDialogRef = ref()
const invoiceHeaderListRef = ref()
const customerAddressRef = ref()

const message = useMessage() // 消息弹窗
const emits = defineEmits(['success'])

/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true
  detailInfo.value = {
    id: id
  }
  await getCustomerGroupCount(id)
}

const close = function () {
  dialogVisible.value = false
  detailInfo.value = null
}

const onBeforeSetColumns = (columns: ColumnVo[]) => {
  addDetailCommonColumns(columns)
}

/** 锁定/解锁 */
const handleBatchLock = async (ids: any, isLock: boolean) => {
  if (isLock) {
    await message.confirm('确认锁定吗?')
    await batchLock({customerIdList: ids})
    message.success('锁定成功')
  } else {
    await message.confirm('确认解锁吗?')
    await batchUnLock({customerIdList: ids})
    message.success('解锁成功')
  }
  formRef.value.reload()
}

/** 放入公海 */
const handlePutSeas = async (ids: any) => {
  putSeasDialogRef.value.open(ids)
}

/** 删除按钮操作 */
const handleDelete = async (ids: number[]) => {
  await message.confirm('确认删除吗?')
  await MainApi.batchDeleteCustomer(ids)
  message.success('删除成功')
  close()
  emits('success')
}

/** 关注/取消关注 */
const updateStar = async (row: any) => {
  await updateCollection(row.id, CrmModelTypeEnum.CUSTOMER, row.collectionId)
  formRef.value.reload()
}

/** 成交/未成交 */
const handleDeal = async (ids: any, isFinish: boolean) => {
  if (isFinish) {
    await message.confirm('确认成交吗?')
    await batchFinish({customerIdList: ids})
    message.success('成交成功')
  } else {
    await message.confirm('确认未成交吗?')
    await batchFail({customerIdList: ids})
    message.success('未成交成功')
  }
  formRef.value.reload()
}

/** 查询详情 */
const getDetail = async (id: number, done: Function, error: Function) => {
  try {
    const data = await MainApi.getCustomer(id)
    done(data)
  } catch {
    error()
  }
}

/** 统计数量 */
const getCustomerGroupCount = async (id: number) => {
  const data = await MainApi.groupCount({customerId: id})
  customerGroupCount.value = data
}

/** 打开上级客户列表 */
const openParentCustomerList = () => {
  if (!detailInfo.value?.id) {
    message.warning('请先选择客户')
    return
  }
  customerRelationListDialogRef.value.open('parent')
}

/** 打开下级客户列表 */
const openSubCustomerList = () => {
  if (!detailInfo.value?.id) {
    message.warning('请先选择客户')
    return
  }
  customerRelationListDialogRef.value.open('sub')
}

const onPutSeasSuccess = () => {
  emits('success')
  close()
}

const onCreateSuccess = (id: any) => {
  getCustomerGroupCount(id)
  formRef.value.reload()
}

const defaultVal = computed(() => {
  return {customerId: detailInfo.value?.id, customerName: detailInfo.value?.name}
})

defineExpose({open})
</script>

<style scoped lang="scss">
.detail-body {
  display: flex;
  height: 100%;

  .detail-main {
    width: 550px;
    display: flex;
    flex-direction: column;

    .detail-main-relation {
      display: flex;
      align-items: center;
      border: 1px solid var(--el-border-color);
      border-radius: 10px;
      margin-bottom: 20px;
      flex-flow: wrap row;
      padding: 5px;
      margin-top: 10px;

      button {
        width: calc(20% - 10px);
        margin: 5px 5px;
        background: #f8f8f8;
        border: 0;
      }
    }

    .detail-main-form {
      border: 1px solid var(--el-border-color);
      padding: 10px;
      border-radius: 10px;
      flex: 1;
    }
  }
}
</style>
