<template>
  <el-drawer class="z-detail-drawer" v-model="dialogVisible" @close="close" size="1200px">
    <template #header>
      <div class="z-detail-drawer-header">
        <div class="title">
          <div class="title-icon">
            <el-icon v-if="detailInfo?.collectionId" size="25" style="color: var(--el-color-warning)">
              <StarFilled/>
            </el-icon>
            <el-icon style="color: #9a9a9a" size="20" v-else>
              <Star/>
            </el-icon>
          </div>
          <div class="title-label">
            {{ detailInfo?.name || '商机详情' }}
          </div>
        </div>
        <div class="tool">
          <el-button @click="handleTransferLeader([detailInfo?.id])"
                     v-if="detailInfo?.teamPermission === permission.rw"
                     v-hasPermi="['crm:team:transfer-leader']">
            转移负责人
          </el-button>
          <el-button type="primary" @click="businessFormRef.open(detailInfo?.id)" v-hasPermi="['crm:business:update']"
                     v-if="detailInfo?.teamPermission === permission.rw">
            编辑
          </el-button>
          <el-button type="danger" @click="handleDelete([detailInfo?.id])" v-hasPermi="['crm:business:delete']"
                     v-if="detailInfo?.teamPermission === permission.rw">删除
          </el-button>
        </div>
      </div>
    </template>
    <div class="detail-body">
      <div class="detail-main" v-if="detailInfo">
        <div class="detail-main-create">
          <el-button link type="primary" @click="contactFormRef.open()" v-hasPermi="['crm:contact:create']">
            <el-icon>
              <CirclePlusFilled/>
            </el-icon>
            创建联系人
          </el-button>
          <el-button link type="primary" @click="contractFormRef.open()" v-hasPermi="['crm:contract:create']">
            <el-icon>
              <CirclePlusFilled/>
            </el-icon>
            创建合同
          </el-button>
          <el-button link type="primary" @click="receivableFormRef.open()" v-hasPermi="['crm:receivable:create']">
            <el-icon>
              <CirclePlusFilled/>
            </el-icon>
            创建回款
          </el-button>
          <el-button link type="primary" @click="tagSelectorRef.open(detailInfo?.id, detailInfo?.tagIdList)"
                     v-hasPermi="['crm:tag-relation:create']">
            <el-icon>
              <Flag/>
            </el-icon>
            打标签
          </el-button>
        </div>
        <div class="detail-main-relation">
          <el-button @click="teamListRef.open()">团队成员</el-button>
          <el-button @click="contractListRef.open()">合同</el-button>
          <el-button @click="receivableListRef.open()">回款</el-button>
          <el-button @click="contactListRef.open()">联系人</el-button>
        </div>
        <div class="detail-main-form">
          <ZCustomDetail ref="formRef" page-key="crmBusiness" :is-custom="true" v-model="detailInfo"
                         :data-id="detailInfo.id" @get-detail="getDetail" @before-set-columns="onBeforeSetColumns">

            <!-- 客户名称 -->
            <template #form-prop-customerId>
              {{ detailInfo.customerName || '-' }}
            </template>

            <!-- 商机阶段 -->
            <template #form-prop-stageId>
              {{ detailInfo.stageName || '-' }}
            </template>

            <!-- 商机状态组 -->
            <template #form-prop-stepId>
              {{ detailInfo.stepName || '-' }}
            </template>

            <!-- 创建人 -->
            <template #form-prop-creator>
              {{ detailInfo.creatorName || '-' }}
            </template>

            <!-- 更新人 -->
            <template #form-prop-updater>
              {{ detailInfo.updaterName || '-' }}
            </template>
          </ZCustomDetail>
        </div>
        <div class="step-panel">
          <StepDetailPanel v-if="detailInfo?.id" :target-type="CrmModelTypeEnum.BUSINESS" :target-id="detailInfo?.id"/>
        </div>
      </div>
      <div class="detail-dashboard">
        <CustomerDetailExpand v-if="detailInfo?.id" :target-type="CrmModelTypeEnum.BUSINESS"
                              :target-id="detailInfo?.id"/>
      </div>
    </div>

    <div class="follow-section" v-if="false">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="跟进记录" name="follow">
          <FollowRecord @success="formRef.reload()" v-if="detailInfo?.id && activeTab === 'follow'"
                        :target-type="CrmModelTypeEnum.BUSINESS" :target-id="detailInfo?.id"/>
        </el-tab-pane>
        <el-tab-pane label="合同" name="contract">
          <ContractList v-if="detailInfo?.name && activeTab === 'contract'"
                        :condition="{businessId: detailInfo?.id, customerId: detailInfo?.customerId, customerName: detailInfo?.customerName, businessName: detailInfo?.name}"
                        :disabled-props="['businessId', 'customerId']"
                        :default-val="{businessId: detailInfo?.id, customerId: detailInfo?.customerId, customerName: detailInfo?.customerName, businessName: detailInfo?.name}"/>
        </el-tab-pane>
        <el-tab-pane label="团队成员" name="team">
          <TeamUser v-if="detailInfo?.id && activeTab === 'team'" :target-type="CrmModelTypeEnum.BUSINESS"
                    :target-id="detailInfo?.id"/>
        </el-tab-pane>
        <el-tab-pane label="操作记录" name="operation">
          <OperationRecord v-if="detailInfo?.id && activeTab === 'operation'" :target-type="CrmModelTypeEnum.BUSINESS"
                           :target-id="detailInfo?.id"/>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-drawer>

  <ContactForm ref="contactFormRef"
               :default-val="{customerId: detailInfo?.customerId, customerName: detailInfo?.customerName}"
               :disabled-props="['customerId']"/>
  <ContractForm ref="contractFormRef"
                :default-val="{customerId: detailInfo?.customerId, businessId: detailInfo?.id, customerName: detailInfo?.customerName, businessName: detailInfo?.name}"
                :disabled-props="['customerId', 'businessId']"/>
  <BusinessForm ref="businessFormRef" @complete="formRef.reload()"/>
  <ReceivableForm ref="receivableFormRef"
                  :default-val="{customerId: detailInfo?.customerId, customerName: detailInfo?.customerName}"
                  :disabled-props="['customerId']"/>
  <PowerLeaderTransfer ref="transferLeaderRef" :targetType="CrmModelTypeEnum.BUSINESS"/>
  <TagSelectSelector ref="tagSelectorRef" :targetType="CrmModelTypeEnum.BUSINESS" @success="formRef.reload()"/>

  <TeamListDialog v-if="detailInfo?.id"
                  :title="detailInfo?.name"
                  :targetType="CrmModelTypeEnum.BUSINESS"
                  :target-id="detailInfo?.id"
                  ref="teamListRef"/>

  <ContractListDialog v-if="detailInfo?.id"
                      :title="detailInfo?.name"
                      :condition="defaultVal"
                      :default-val="defaultVal"
                      :disabled-props="['customerId', 'businessId']"
                      ref="contractListRef"/>

  <ReceivableList v-if="detailInfo?.id"
                  :title="detailInfo?.name"
                  :condition="defaultVal"
                  :default-val="defaultVal"
                  :disabled-props="['customerId']"
                  ref="receivableListRef"/>

  <ContactListDialog v-if="detailInfo?.id"
                     :title="detailInfo?.name"
                     :condition="defaultVal"
                     :default-val="defaultVal"
                     :disabled-props="['customerId']"
                     ref="contactListRef"/>
</template>
<script lang="ts" setup>
import * as MainApi from '@/api/crm/business'
import {ColumnVo} from '@/components/Zeadoor/interface'
import {addDetailCommonColumns, CrmModelTypeEnum} from '@/api/crm/common'
import {CirclePlusFilled, Flag, Star, StarFilled} from "@element-plus/icons-vue";
import ContactForm from "@/views/crm/contact/form.vue";
import ContractForm from "@/views/crm/contract/form.vue";
import BusinessForm from "@/views/crm/business/form.vue";
import ReceivableForm from "@/views/crm/receivable/form.vue";
import PowerLeaderTransfer from '@/views/crm/common/transfer/power-leader.vue'
import TagSelectSelector from '@/views/crm/common/tag/tag-select.vue'
import FollowRecord from "@/views/crm/common/follow/follow-record.vue";
import OperationRecord from "@/views/system/operation/operation.vue";
import TeamUser from "@/views/crm/common/team/index.vue";
import StepDetailPanel from "@/views/crm/common/step/step-detail.vue";
import ContractList from "@/views/crm/contract/index.vue";
import {permission} from "@/api/crm/team";
import CustomerDetailExpand from "@/views/crm/business/detail/expand.vue";
import TeamListDialog from "@/views/crm/common/team/components/team-dialog.vue";
import ContractListDialog from "@/views/crm/contract/components/contract-dialog.vue";
import ReceivableList from "@/views/crm/receivable/components/receivable-dialog.vue";
import ContactListDialog from "@/views/crm/contact/components/contact-dialog.vue";

const activeTab = ref('follow') // 默认选中跟进记录标签
const message = useMessage() // 消息弹窗

const detailInfo = ref()
const dialogVisible = ref(false) // 弹窗的是否展示
const contactFormRef = ref()
const formRef = ref()
const contractFormRef = ref()
const businessFormRef = ref()
const receivableFormRef = ref()
const tagSelectorRef = ref()
const teamListRef = ref()
const contractListRef = ref()
const receivableListRef = ref()
const contactListRef = ref()

const emits = defineEmits(['success'])

/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true
  detailInfo.value = {
    id: id
  }
}
const close = function () {
  dialogVisible.value = false
  detailInfo.value = null
}

const onBeforeSetColumns = (columns: ColumnVo[]) => {
  addDetailCommonColumns(columns)
}

/** 查询详情 */
const getDetail = async (id: number, done: Function, error: Function) => {
  try {
    const data = await MainApi.getBusiness(id)
    done(data)
  } catch {
    error()
  }
}

/** 删除按钮操作 */
const handleDelete = async (ids: number[]) => {
  await message.confirm('确认删除吗?')
  await MainApi.batchDeleteBusiness(ids)
  message.success('删除成功')
  close()
  emits('success')
}

const defaultVal = computed(() => {
  return {customerId: detailInfo.value?.customerId, customerName: detailInfo.value?.customerName, businessId: detailInfo.value?.id, businessName: detailInfo.value?.name}
})

/** 转移负责人 */
const transferLeaderRef = ref()
const handleTransferLeader = (ids: number[]) => {
  transferLeaderRef.value.open({
    targetIdList: ids
  })
}

defineExpose({open})
</script>

<style scoped lang="scss">
.detail-body {
  display: flex;
  height: 100%;

  .detail-main {
    width: 550px;
    display: flex;
    flex-direction: column;

    .detail-main-relation {
      display: flex;
      align-items: center;
      border: 1px solid var(--el-border-color);
      border-radius: 10px;
      margin-bottom: 20px;
      flex-flow: wrap row;
      padding: 5px;
      margin-top: 10px;

      button {
        width: calc(20% - 10px);
        margin: 5px 5px;
        background: #f8f8f8;
        border: 0;
      }
    }

    .detail-main-form {
      border: 1px solid var(--el-border-color);
      padding: 10px;
      border-radius: 10px;
      flex: 1;
    }
  }
}
</style>
