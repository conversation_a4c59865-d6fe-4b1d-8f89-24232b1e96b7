<template>
  <ZCustomTable ref="tableRef" :config="config" @get-list="getList"
                @delete="handleDelete">
    <template #column-prop-role="scope">
      <font v-if="scope.row.role === '10'">负责人</font>
      <font v-else-if="scope.row.role === '20'">普通成员</font>
    </template>
    <template #column-prop-permission="scope">
      <font v-if="scope.row.permission === permission.rw">读写</font>
      <font v-else-if="scope.row.permission === permission.r">只读</font>
    </template>
    <template #column-prop-effectiveTime="scope">
      <font v-if="scope.row.isEffective">不限</font>
      <font v-else-if="scope.row.effectiveTime">{{ scope.row.effectiveTime }}</font>
    </template>
  </ZCustomTable>
  <CurrentForm ref="formRef" @complete="tableRef.reload()" :target-type="targetType" :target-id="targetId"/>
</template>

<script setup lang="ts">
import CurrentForm from './form.vue'
import * as MainApi from '@/api/crm/team/index'
import {ConfigBO} from '@/components/Zeadoor/CustomTable/config'
import {merge} from 'lodash-es'
import {SearchToolAddButton, TableToolDeleteButton, TableToolUpdateButton} from '@/components/Zeadoor/CustomTable/table'
import {propTypes} from "@/utils/propTypes";
import {permission} from "@/api/crm/team/index";
const message = useMessage()
const props = defineProps({
  targetType: propTypes.object.def(),
  targetId: propTypes.object.def(),
  defaultVal: propTypes.object.def(),
})

const config = ref<ConfigBO>({
  table: {
    columns: [
      {
        label: "姓名",
        prop: "customerName"
      },
      {
        label: "部门",
        width: '200px',
        prop: "deptName"
      },
      {
        label: "团队角色",
        prop: "role"
      },
      {
        label: "有效时间",
        prop: "effectiveTime"
      },
      {
        label: "权限",
        prop: "permission"
      }
    ],
    toolbar: {
      width: 120,
      buttons: [
        merge({}, TableToolUpdateButton, {
          click: (scope: any) => {
            formRef.value.open(scope.row.id)
          },
          vIf: (scope: any) => {
            return scope.row.role !== '10'
          }
        }),
        merge({}, TableToolDeleteButton, {
          vIf: (scope: any) => {
            return scope.row.role !== '10'
          }
        }),
      ]
    }
  },
  search: {
    show: true,
    showMore: false,
    toolbar: {
      buttons: [
        merge({}, SearchToolAddButton, {
          click: () => {
            formRef.value.open()
          }
        }),
        {
          label: "退出团队",
          click: () => {
            onExit()
          }
        }
      ]
    },
    defaultQuery: {
      type: 1
    }
  }
})
const formRef = ref()
const tableRef = ref()

/** 查询列表 */
const getList = async (queryParams: any, done: Function, error: Function) => {
  try {
    const data = await MainApi.getTeamPage(Object.assign({}, queryParams, props))
    done(data.list, data.total)
  } catch {
    error()
  }
}

/** 删除按钮操作 */
const handleDelete = async (ids: number[], done: Function, error: Function) => {
  try {
    await MainApi.deleteCustomer(ids[0])
    done()
  } catch {
    error()
  }
}

/** 退出团队操作 */
const onExit = async () => {
  try {
    await message.confirm('确定退出团队吗?')
    await MainApi.exitTeam(props)
    message.notifySuccess('退出成功')
    tableRef.value.reload()
  } catch {}
}
</script>
