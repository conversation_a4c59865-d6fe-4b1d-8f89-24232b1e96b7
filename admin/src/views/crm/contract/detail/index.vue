<template>
  <el-drawer class="z-detail-drawer" v-model="dialogVisible" @close="close" size="1200px">
    <template #header>
      <div class="z-detail-drawer-header">
        <div class="title">
          <div class="title-icon">
            <el-icon v-if="detailInfo?.isLock" size="20" style="color: var(--el-color-danger)">
              <Lock/>
            </el-icon>
            <el-icon style="color: #9a9a9a" size="20" v-else>
              <Unlock/>
            </el-icon>
          </div>
          <div class="title-label">
            {{ detailInfo?.name || '合同详情' }}
          </div>
          <div>
            <dict-tag class="m-l-2 m-r-2" :type="DICT_TYPE.OA_AUDIT_STATUS" :value="detailInfo?.approvalStatus"/>
            <!--            <span class="is-link" style="font-size: 12px;font-weight: 500" v-if="detailInfo?.approvalOrderId" @click="push({ name: 'BpmProcessInstanceDetail', query: { id: detailInfo.approvalOrderId } })">{{detailInfo?.approvalOrderId}}</span>-->
          </div>
        </div>
        <div class="tool">
          <el-button @click="handleTransferLeader([detailInfo?.id])" v-hasPermi="['crm:team:transfer-leader']">
            转移负责人
          </el-button>
          <el-button type="primary" @click="contractFormRef.open(detailInfo?.id)" v-hasPermi="['crm:contract:update']">
            编辑
          </el-button>
          <el-button type="danger" @click="handleDelete([detailInfo?.id])" v-hasPermi="['crm:contract:delete']">删除
          </el-button>
        </div>
      </div>
    </template>

    <div class="detail-body">
      <div class="detail-main" v-if="detailInfo">
        <div class="detail-main-create">
          <el-button link type="primary" @click="receivableFormRef.open()" v-hasPermi="['crm:receivable:create']">
            <el-icon>
              <CirclePlusFilled/>
            </el-icon>
            创建回款
          </el-button>
          <el-button link type="primary" @click="visitFormRef.open()">
            <el-icon>
              <CirclePlusFilled/>
            </el-icon>
            创建回访
          </el-button>
          <el-button link type="primary" @click="invoiceFormRef.open()">
            <el-icon>
              <CirclePlusFilled/>
            </el-icon>
            创建发票
          </el-button>
        </div>

        <div class="detail-main-relation">
          <el-button @click="teamListRef.open()">团队成员</el-button>
          <el-button @click="planListRef.open()">回款计划</el-button>
          <el-button @click="receivableListRef.open()">回款</el-button>
          <el-button @click="visitListRef.open()">回访</el-button>
          <el-button @click="invoiceListRef.open()">发票</el-button>
        </div>
        <div class="detail-main-form">
          <DetailPanel v-model="detailInfo" v-if="detailInfo?.id"/>
        </div>
        <div class="step-panel">
          <StepDetailPanel v-if="detailInfo?.id" :target-type="CrmModelTypeEnum.CONTRACT" :target-id="detailInfo?.id"/>
        </div>
      </div>
      <div class="detail-dashboard">
        <CustomerDetailExpand v-if="detailInfo?.id" :target-type="CrmModelTypeEnum.CONTRACT" :target-id="detailInfo?.id"/>
      </div>
    </div>
  </el-drawer>

  <PowerLeaderTransfer ref="transferLeaderRef" :targetType="CrmModelTypeEnum.CONTRACT"/>
  <CustomerForm ref="customerFormRef" @complete="formRef.reload()"/>
  <ContactForm ref="contactFormRef" :default-val="{customerId: detailInfo?.id}" @complete="formRef.reload()"/>
  <ContractForm ref="contractFormRef" @complete="formRef.reload()"/>
  <BusinessForm ref="businessFormRef" :default-val="{customerId: detailInfo?.id}"/>
  <ReceivableForm ref="receivableFormRef" :disabled-props="['customerId','contractId']" :default-val="defaultVal"/>
  <VisitForm ref="visitFormRef" :disabled-props="['customerId','contractId']" :default-val="defaultVal"/>
  <InvoiceForm ref="invoiceFormRef" :disabled-props="['customerId','contractId']" :default-val="defaultVal"/>

  <InvoiceListDialog v-if="detailInfo?.id"
                     :title="detailInfo?.name"
                     :condition="defaultVal"
                     :disabled-props="['customerId','contractId']"
                     :default-val="defaultVal"
                     ref="invoiceListRef"/>

  <VisitListDialog v-if="detailInfo?.id"
                   :title="detailInfo?.name"
                   :condition="defaultVal"
                   :disabled-props="['customerId','contractId']"
                   :default-val="defaultVal"
                   ref="visitListRef"/>
  <PlanListDialog v-if="detailInfo?.id"
                  :title="detailInfo?.name"
                  :condition="defaultVal"
                  :disabled-props="['customerId','contractId']"
                  :default-val="defaultVal"
                  ref="planListRef"/>
  <ReceivableList v-if="detailInfo?.id"
                  :title="detailInfo?.name"
                  :condition="defaultVal"
                  :disabled-props="['customerId','contractId']"
                  :default-val="defaultVal"
                  ref="receivableListRef"/>
  <TeamListDialog v-if="detailInfo?.id"
                  :title="detailInfo?.name"
                  :targetType="CrmModelTypeEnum.CONTRACT"
                  :target-id="detailInfo?.id"
                  ref="teamListRef"/>
</template>
<script lang="ts" setup>
import * as MainApi from '@/api/crm/contract'
import {CrmModelTypeEnum} from '@/api/crm/common'
import {CirclePlusFilled, Lock, Unlock} from "@element-plus/icons-vue";
import ContactForm from "@/views/crm/contract/form.vue";
import ContractForm from "@/views/crm/contract/form.vue";
import BusinessForm from "@/views/crm/business/form.vue";
import ReceivableForm from "@/views/crm/receivable/form.vue";
import VisitForm from "@/views/crm/visit/form.vue";
import InvoiceForm from "@/views/crm/invoice/form.vue";
import CustomerForm from "@/views/crm/customer/form.vue";
import PowerLeaderTransfer from "@/views/crm/common/transfer/power-leader.vue";
import StepDetailPanel from "@/views/crm/common/step/step-detail.vue";
import DetailPanel from "./detail.vue"
import {DICT_TYPE} from '@/utils/dict'
import PlanListDialog from '@/views/crm/receivable/components/plan-dialog.vue'
import ReceivableList from '@/views/crm/receivable/components/receivable-dialog.vue'
import InvoiceListDialog from '@/views/crm/invoice/components/invoice-dialog.vue'
import VisitListDialog from '@/views/crm/visit/components/visit-dialog.vue'
import TeamListDialog from '@/views/crm/common/team/components/team-dialog.vue'
import CustomerDetailExpand from "@/views/crm/contract/detail/expand.vue";

const message = useMessage() // 消息弹窗
const detailInfo = ref()
const customerFormRef = ref()
const formRef = ref()
const dialogVisible = ref(false) // 弹窗的是否展示
const contactFormRef = ref()
const contractFormRef = ref()
const businessFormRef = ref()
const receivableFormRef = ref()
const visitFormRef = ref()
const invoiceFormRef = ref()

const planListRef = ref()
const receivableListRef = ref()
const visitListRef = ref()
const invoiceListRef = ref()
const teamListRef = ref()

const {push} = useRouter() // 路由
const emits = defineEmits(['success'])

/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true
  detailInfo.value = {
    id: id
  }
}
const close = function () {
  dialogVisible.value = false
  detailInfo.value = null
}

/** 删除按钮操作 */
const handleDelete = async (ids: number[]) => {
  await message.confirm('确认删除吗?')
  await MainApi.deleteContract(ids)
  message.success('删除成功')
  close()
  emits('success')
}

/** 转移负责人 */
const transferLeaderRef = ref()
const handleTransferLeader = (ids: number[]) => {
  transferLeaderRef.value.open({
    targetIdList: ids
  })
}

/** 查询详情 */
const getDetail = async (id: number, done: Function, error: Function) => {
  try {
    const data = await MainApi.getContract(id)
    done(data)
  } catch {
    error()
  }
}

const defaultVal = computed(() => {
  return {
    contractId: detailInfo.value?.id, contractName: detailInfo.value?.name,
    customerId: detailInfo.value?.customerId, customerName: detailInfo.value?.customerName
  }
})

defineExpose({open})
</script>

<style scoped lang="scss">
.detail-body {
  display: flex;
  height: 100%;

  .detail-main {
    width: 600px;
    display: flex;
    flex-direction: column;

    .detail-main-relation {
      display: flex;
      align-items: center;
      border: 1px solid var(--el-border-color);
      border-radius: 10px;
      margin-bottom: 20px;
      flex-flow: wrap row;
      padding: 5px;
      margin-top: 10px;

      button {
        width: calc(20% - 10px);
        margin: 5px 5px;
        background: #f8f8f8;
        border: 0;
      }
    }

    .detail-main-form {
      border: 1px solid var(--el-border-color);
      padding: 10px;
      border-radius: 10px;
      flex: 1;
    }
  }
}
</style>
