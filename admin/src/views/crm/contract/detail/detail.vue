<template>
  <ZCustomDetail v-if="detailInfo.id" page-key="crmContract" :is-custom="true" ref="formRef" v-model="detailInfo"
                 :data-id="detailInfo.id" @get-detail="getDetail" @before-set-columns="onBeforeSetColumns">
    <template #form-prop-customerId>
      {{ detailInfo.customerName || '-' }}
    </template>

    <template #form-prop-businessId>
      {{ detailInfo.businessName || '-' }}
    </template>

    <template #form-prop-customerSignId>
      {{ detailInfo.customerSignName || '-' }}
    </template>

    <template #form-prop-companySignId>
      {{ detailInfo.companySignName || '-' }}
    </template>
  </ZCustomDetail>
</template>
<script lang="ts" setup>
import {ColumnVo} from '@/components/Zeadoor/interface'
import {addDetailCommonColumns} from '@/api/crm/common'
import {propTypes} from "@/utils/propTypes";
import * as MainApi from '@/api/crm/contract'

const props = defineProps({
  id: propTypes.number.def(),
  modelValue: propTypes.string.def(),
})
const detailInfo = ref(props.modelValue || {id: props.id})
const formRef = ref()
const emits = defineEmits(['success', 'update:modelValue'])
const onBeforeSetColumns = (columns: ColumnVo[]) => {
  addDetailCommonColumns(columns)
}
/** 查询详情 */
const getDetail = async (id: number, done: Function, error: Function) => {
  try {
    const data = await MainApi.getContract(id)
    done(data)
  } catch {
    error()
  }
}

watch(() => detailInfo.value, function () {
  emits("update:modelValue", detailInfo.value)
})
</script>
