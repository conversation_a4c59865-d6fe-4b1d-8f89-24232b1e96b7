<template>
  <div class="detail-expand">
    <div class="record">
      <div class="tab">
        <el-tabs v-model="activeTab" type="card">
          <el-tab-pane label="人工跟进" name="follow"/>
          <el-tab-pane label="时间轴" name="operation"/>
        </el-tabs>
      </div>
      <div class="center">
        <FollowRecord v-if="targetId && activeTab === 'follow'"
                      :target-type="targetType" :target-id="targetId"/>
        <OperationRecord v-if="targetId && activeTab === 'operation'"
                         :target-type="targetType"
                         :target-id="targetId"/>
      </div>
    </div>
  </div>
  <TagSelectSelector ref="tagSelectorRef" :targetType="targetType" @success="getTagList"/>
</template>

<script lang="ts" setup>
import {propTypes} from "@/utils/propTypes";
import FollowRecord from "@/views/crm/common/follow/follow-record.vue";
import OperationRecord from "@/views/system/operation/operation.vue";
import {Plus, WarningFilled} from "@element-plus/icons-vue";
import TagSelectSelector from "@/views/crm/common/tag/tag-select.vue";
import AiRiskPanel from "@/views/crm/common/ai/risk.vue"
import AiTagPanel from "@/views/crm/common/ai/tag.vue"
import AiSummaryPanel from "@/views/crm/common/ai/summary.vue"
import AiCallLogPanel from "@/views/crm/common/ai/callLog.vue"
import StageDetailPanel from "@/views/crm/common/stage/detail.vue"
import * as TagRelationApi from '@/api/crm/tag/relation'

const props = defineProps({
  targetType: propTypes.object.def(),
  targetId: propTypes.object.def(),
})
const activeTab = ref("follow")
const tagSelectorRef = ref()

//获取标签数据
const tagList = ref([])
const tagLoading = ref(true)
const getTagList = async () => {
  try {
    tagLoading.value = true
    let data = await TagRelationApi.getTagRelationPage({
      targetType: props.targetType,
      targetId: props.targetId
    })
    tagList.value = data.list
  } finally {
    tagLoading.value = false
  }
}

onMounted(() => {
  getTagList()
})
</script>

<style lang="scss">
.detail-expand {
  width: 100%;
  display: flex;
  height: 100%;

  .record {
    flex: 1;
    display: flex;
    flex-direction: column;

    .tab {
      flex-shrink: 0;
    }

    .center {
      flex: 1;
      height: 0;
      display: flex;
      flex-direction: column;

      .zeadoor-list-container {
        flex: 1;
        height: 0;
      }

      .operation {
        display: flex;
        flex-direction: column;
        flex: 1;
        height: 0;

        .operation-record {
          flex: 1;
          overflow: auto;
        }
      }
    }
  }

  .ai {
    width: 300px;
    padding-left: 20px;
    display: flex;
    height: 100%;
    flex-direction: column;

    .ai-span {
      padding: 10px;
      background: #f7f8fa;
      border-radius: 5px;
      margin-bottom: 10px;

      &.stage {
        flex: 1;
      }

      .ai-span-title {
        display: flex;
        align-items: center;

        .label {
          font-size: 15px;
          font-weight: bold;
          display: flex;
          align-items: center;
        }

        .btn {

          display: flex;
          align-items: center;
        }
      }

      .ai-span-center {
        padding-top: 10px;
      }
    }
  }
}
</style>
