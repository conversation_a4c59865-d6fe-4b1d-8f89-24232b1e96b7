<template>
  <ZCustomTable ref="tableRef" page-key="crmContract" :is-custom="true" :config="config" @get-list="getList"
                @delete="handleDelete" @import="(e: any) => MainApi.importData(e)"
                @before-set-columns="onBeforeSetColumns">
    <template #header="{queryForm}">
      <el-tabs v-model="queryForm.pageType" @tabChange="tableRef.reload()">
        <el-tab-pane label="全部合同" :name="0"/>
        <el-tab-pane label="我负责的" :name="1"/>
      </el-tabs>
    </template>
    <template #table-tree="{queryForm}">
      <ContentWrap class="h-1/1 m-r-4" v-if="queryForm.pageType === 0" style="width: 300px">
        <DeptUserTree @node-click="handleNodeClick" />
      </ContentWrap>
    </template>
    <!-- 客户名称 -->
    <template #search-prop-customerId="scope">
      <el-select v-model="scope.form.customerId" filterable show-arrow clearable placeholder="请选择客户名称"
                 :disabled="scope.disabled" remote :remote-method="getCustomerSelectList">
        <el-option
            v-for="item in getSelectRemoteFullList(customerSelectList, scope.form.customerId, scope.form.customerName)"
            :key="item.id" :label="item.name" :value="item.id"/>
      </el-select>
    </template>
    <!-- 商机名称 -->
    <template #search-prop-businessId="scope">
      <el-select v-model="scope.form.businessId" filterable show-arrow clearable placeholder="请选择商机名称"
                 :disabled="scope.disabled" remote :remote-method="(query) => getBusinessSelectList(query)">
        <el-option
            v-for="item in getSelectRemoteFullList(businessSelectList, scope.form.businessId, scope.form.businessName)"
            :key="item.id" :label="item.name" :value="item.id"/>
      </el-select>
    </template>

    <template #column-prop-no="scope">
      <div class="is-link" @click="contractDetailRef.open(scope.row.id)">{{ scope.row.no }}</div>
    </template>

    <!-- 客户名称 -->
    <template #column-prop-customerId="scope">
      <div class="is-link" @click="customerDetailRef.open(scope.row.customerId)">{{
          scope.row.customerName || ''
        }}
      </div>
    </template>
    <!-- 商机名称 -->
    <template #column-prop-businessId="scope">
      <div class="is-link" @click="businessDetailRef.open(scope.row.businessId)">{{
          scope.row.businessName || ''
        }}
      </div>
    </template>

    <template #column-prop-customerSignId="scope">
      {{ scope.row.customerSignName || '' }}
    </template>
    <template #column-prop-companySignId="scope">
      {{ scope.row.companySignName || '' }}
    </template>

    <!-- 未收款金额 -->
    <template #column-prop-unpaidAmount="scope">
      {{ scope.row.amount - (scope.row.receiveAmount || 0) }}
    </template>
  </ZCustomTable>
  <CurrentForm ref="formRef" @complete="tableRef.reload()"
               :disabled-props="disabledProps"
               :default-val="defaultVal"/>
  <ContractDetailPanel ref="contractDetailRef" @success="tableRef.reload()"/>
  <PowerLeaderTransfer ref="transferLeaderRef" :targetType="CrmModelTypeEnum.CONTRACT" @success="tableRef.reload()"/>
  <SelectInstanceTimeLineDialog ref="selectInstanceTimeLineDialogRef" @success="handleApproval"/>
  <CustomerDetailPanel ref="customerDetailRef" @success="tableRef.reload()"/>
  <BusinessDetailPanel ref="businessDetailRef" @success="tableRef.reload()"/>
</template>

<script setup lang="ts">
import CurrentForm from './form.vue'
import {getBusinessPage} from '@/api/crm/business'
import {getContactPage} from '@/api/crm/contact'
import * as MainApi from '@/api/crm/contract/index'
import {ConfigBO} from '@/components/Zeadoor/CustomTable/config'
import SelectInstanceTimeLineDialog from "@/views/bpm/components/selectInstanceTimeLine.vue";
import {merge} from 'lodash-es'
import {
  SearchToolAddButton,
  SearchToolExportButton,
  SearchToolImportButton,
  TableMultipleToolDeleteButton,
  TableToolApprovalButton,
  TableToolDeleteButton,
  TableToolUpdateButton
} from '@/components/Zeadoor/CustomTable/table'
import {ColumnTypeVo, ColumnVo} from '@/components/Zeadoor/interface'
import {BpmProcessInstanceStatus} from "@/utils/constants";
import {CrmModelTypeEnum, customerSelectList, getCustomerSelectList} from '@/api/crm/common'
import PowerLeaderTransfer from '@/views/crm/common/transfer/power-leader.vue'
import ContractDetailPanel from "@/views/crm/contract/detail/index.vue";
import {DICT_TYPE, getSelectRemoteFullList} from '@/utils/dict'
import {propTypes} from "@/utils/propTypes";
import {ProcessDefinition} from "@/api/bpm/definition";
import DeptUserTree from "@/views/system/user/DeptUserTree.vue";
import CustomerDetailPanel from "@/views/crm/customer/detail/index.vue";
import BusinessDetailPanel from "@/views/crm/business/detail/index.vue";

const props = defineProps({
  condition: propTypes.object.def({}),
  defaultVal: propTypes.object.def({}),
  disabledProps: propTypes.object.def([])
})

const {push} = useRouter() // 路由
const selectInstanceTimeLineDialogRef = ref()
const message = useMessage() // 消息弹窗
const config = ref<ConfigBO>({
  table: {
    showSelection: true,
    toolbar: {
      width: 200,
      buttons: [
        merge({}, TableToolApprovalButton, {
          hasPermission: ['crm:contract:approval'],
          vDisabled: (scope: any) => {
            return scope.row.approvalStatus != BpmProcessInstanceStatus.NOT_START
          },
          click: (scope: any) => {
            selectInstanceTimeLineDialogRef.value.open(ProcessDefinition.CrmContract, scope.row.id)
          }
        }),
        merge({}, TableToolUpdateButton, {
          hasPermission: ['crm:contract:update'],
          vDisabled: (scope: any) => {
            return scope.row.approvalStatus === BpmProcessInstanceStatus.RUNNING
          },
          click: (scope: any) => {
            formRef.value.open(scope.row.id)
          }
        }),
        merge({}, TableToolDeleteButton, {
          vDisabled: (scope: any) => {
            return scope.row.approvalStatus === BpmProcessInstanceStatus.RUNNING
          }
        }),
      ]
    },
    multipleToolBar: {
      show: true,
      buttons: [
        merge({}, TableMultipleToolDeleteButton, {
          hasPermission: ['crm:contract:delete']
        }),
        // 转移负责人
        {
          key: 'batchTransferLeader',
          label: '转移负责人',
          hasPermission: ['crm:team:transfer-leader'],
          plain: true,
          click: (ids: any) => {
            handleTransferLeader(ids)
          }
        }
      ]
    }
  },
  search: {
    show: true,
    toolbar: {
      buttons: [
        merge({}, SearchToolAddButton, {
          hasPermission: ['crm:contract:create'],
          click: () => {
            formRef.value.open()
          }
        }),
        merge({}, SearchToolImportButton, {
          hasPermission: ['crm:contract:import']
        }),
        merge({}, SearchToolExportButton, {
          hasPermission: ['crm:contract:export']
        })
      ]
    },
    defaultQuery: merge({}, {pageType: 0}, props?.condition),
    disabledProps: props.disabledProps
  }
})
const formRef = ref()
const tableRef = ref()
const contractDetailRef = ref()
const businessDetailRef = ref()
const customerDetailRef = ref()

const onBeforeSetColumns = (columns: ColumnVo[]) => {
  columns.splice(0, 0, {
    label: '合同编号',
    prop: 'no',
    width: 150,
    align: 'center'
  })
  columns.splice(5, 0, {
    label: '审批状态',
    prop: 'approvalStatus',
    width: 150,
    type: ColumnTypeVo.dict,
    dictKey: DICT_TYPE.OA_AUDIT_STATUS,
    align: 'center'
  })
  columns.splice(6, 0, {
    label: '审批单号',
    prop: 'approvalOrderNo',
    width: 250,
    align: 'left',
    click: (row: any) => {
      push({name: 'BpmProcessInstanceDetail', query: {id: row.approvalOrderId}})
    }
  })
  columns.splice(7, 0, {
    label: '已收款金额(元)',
    prop: 'receiveAmount',
    width: 150,
    align: 'center'
  })
  columns.splice(8, 0, {
    label: '已开票金额(元)',
    prop: 'invoiceAmount',
    width: 150,
    align: 'center'
  })
  columns.splice(9, 0, {
    label: '未收款金额(元)',
    prop: 'unpaidAmount',
    width: 150,
    align: 'center'
  })
  columns.push({
    label: '所属部门',
    prop: 'deptName',
    width: 200,
    align: 'center'
  })
  columns.push({
    label: '更新时间',
    prop: 'updateTime',
    width: 200,
    align: 'center'
  })
  columns.push({
    label: '创建时间',
    prop: 'updateTime',
    width: 200,
    align: 'center'
  })
  columns.push({
    label: '创建人',
    prop: 'creatorName',
    width: 150,
    align: 'center'
  })
}

/** 查询列表 */
const getList = async (queryParams: any, done: Function, error: Function) => {
  try {
    const data = await MainApi.getContractPage(queryParams)
    done(data.list, data.total)
  } catch {
    error()
  }
}

/** 删除按钮操作 */
const handleDelete = async (ids: number[], done: Function, error: Function) => {
  try {
    await MainApi.deleteContract(ids)
    done()
  } catch {
    error()
  }
}

/** 发起审批按钮操作 */
const handleApproval = async (id: number, startUserSelectAssignees: any, done: Function, error: Function) => {
  try {
    // 发起删除
    await MainApi.submitApproval({
      id: id,
      startUserSelectAssignees: startUserSelectAssignees
    })
    message.success("操作成功")
    // 刷新列表
    tableRef.value.reload()
    done()
  } catch {
    error()
  }
}

// 商机选择列表
const businessSelectList = ref([])
const getBusinessSelectList = async (searchKey?: string) => {
  try {
    const res = await getBusinessPage({
      name: searchKey
    })
    businessSelectList.value = res.list
  } finally {
  }
}

// 获取客户签约人选择列表
const customerSignSelectList = ref([])
const getCustomerSignSelectList = async (searchKey?: string) => {
  try {
    const data = await getContactPage({
      name: searchKey
    })
    customerSignSelectList.value = data.list
  } finally {
  }
}

/** 转移负责人 */
const transferLeaderRef = ref()
const handleTransferLeader = (ids: number[]) => {
  transferLeaderRef.value.open({
    targetIdList: ids
  })
}

/**
 * 选择部门或人员
 * @param row
 */
const handleNodeClick = (row: any) => {
  let searchCondition = {
    leaderId: null,
    deptId: null
  }
  switch (row.type) {
    case "dept":
      searchCondition.deptId = row.id
      break
    case "user":
      searchCondition.leaderId = row.id
      break
  }
  tableRef.value.reload(searchCondition)
}
onMounted(() => {
})

</script>
