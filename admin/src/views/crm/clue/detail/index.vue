<template>
  <el-drawer class="z-detail-drawer" v-model="dialogVisible" @close="close" size="1500px">
    <template #header>
      <div class="z-detail-drawer-header">
        <div class="title">
          <div class="title-icon">
            <el-icon @click="updateStar(detailInfo)" v-if="detailInfo?.collectionId" size="25" style="color: var(--el-color-warning)">
              <StarFilled/>
            </el-icon>
            <el-icon @click="updateStar(detailInfo)" style="color: #9a9a9a" size="20" v-else>
              <Star/>
            </el-icon>
          </div>
          <div class="title-label">
            {{ detailInfo?.name || '线索详情' }}
          </div>
        </div>
        <div class="tool">
          <el-button @click="transferLeaderRef.open({ targetIdList: [detailInfo?.id] })"
                     v-hasPermi="['crm:team:transfer-leader']">转移负责人
          </el-button>
          <el-button @click="batchTransCustomer([detailInfo?.id])" v-hasPermi="['crm:clue:transform']">转化客户
          </el-button>
          <el-button type="primary" @click="clueFormRef.open(detailInfo?.id)" v-hasPermi="['crm:clue:update']">编辑
          </el-button>
          <el-button type="danger" @click="handleDelete([detailInfo?.id])" v-hasPermi="['crm:clue:delete']">删除</el-button>
        </div>
      </div>
    </template>

    <div class="detail-body">
      <div class="detail-main" v-if="detailInfo">
        <div class="detail-main-form">
          <!-- 线索信息 -->
          <ZCustomDetail ref="formRef" page-key="crmClue" :is-custom="true" v-model="detailInfo"
                         :data-id="detailInfo.id" @get-detail="getDetail" @before-set-columns="onBeforeSetColumns">

            <!-- 线索阶段 -->
            <template #form-prop-stageId>
              {{ detailInfo.stageName || '-' }}
            </template>

            <!-- 城市 -->
            <template #form-prop-cityCode>
              {{ detailInfo.cityName || ''}}
            </template>
          </ZCustomDetail>

          <div class="step-panel">
            <StepDetailPanel v-if="detailInfo?.id" :target-type="CrmModelTypeEnum.CLUE" :target-id="detailInfo?.id"/>
          </div>
        </div>
      </div>
      <div class="detail-dashboard">
        <CustomerDetailExpand v-if="detailInfo?.id" :target-type="CrmModelTypeEnum.CLUE"
                              :target-id="detailInfo?.id"/>
      </div>
    </div>
  </el-drawer>

  <ClueForm ref="clueFormRef" @complete="formRef.reload()"/>
  <OnlyLeaderTransfer ref="transferLeaderRef" :targetType="CrmModelTypeEnum.CLUE"/>
  <TagSelectSelector ref="tagSelectorRef" :targetType="CrmModelTypeEnum.CLUE" @success="formRef.reload()"/>
</template>
<script lang="ts" setup>
import * as MainApi from '@/api/crm/clue'
import {ColumnVo} from '@/components/Zeadoor/interface'
import { addDetailCommonColumns, CrmModelTypeEnum, updateCollection } from '@/api/crm/common'
import {Star, StarFilled} from '@element-plus/icons-vue'
import ClueForm from '@/views/crm/clue/form.vue'
import OnlyLeaderTransfer from '@/views/crm/common/transfer/only-leader.vue'
import TagSelectSelector from '@/views/crm/common/tag/tag-select.vue'
import StepDetailPanel from "@/views/crm/common/step/step-detail.vue";
import CustomerDetailExpand from "@/views/crm/customer/detail/expand.vue";

const transferLeaderRef = ref()
const detailInfo = ref()
const formRef = ref()
const clueFormRef = ref()
const dialogVisible = ref(false) // 弹窗的是否展示
const tagSelectorRef = ref()
const activeTab = ref('follow') // 默认选中跟进记录标签

const emits = defineEmits(['success'])

/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true
  detailInfo.value = {
    id: id
  }
}

const message = useMessage() // 消息弹窗

const close = function () {
  dialogVisible.value = false
  detailInfo.value = null
}

const onBeforeSetColumns = (columns: ColumnVo[]) => {
  addDetailCommonColumns(columns)
}

/** 查询详情 */
const getDetail = async (id: number, done: Function, error: Function) => {
  try {
    const data = await MainApi.getClue(id)
    done(data)
  } catch {
    error()
  }
}

/** 关注/取消关注 */
const updateStar = async (row: any) => {
  await updateCollection(row.id, CrmModelTypeEnum.CLUE, row.collectionId)
  formRef.value.reload()
}

/** 删除按钮操作 */
const handleDelete = async (ids: number[]) => {
  await message.confirm('确认删除吗?')
  await MainApi.batchDeleteClue(ids)
  message.success('删除成功')
  close()
  emits('success')
}

/** 批量转化客户 */
const batchTransCustomer = async (ids: number[]) => {
  try {
    await message.confirm('确认转化为客户吗?')
    let res = await MainApi.transformClue(ids)
    if (res) {
      // 根据返回结果展示不同的消息
      if (res.successCount > 0 && res.failureCount === 0) {
        message.success(`成功转化${res.successCount}个线索为客户!`)
      } else if (res.successCount > 0 && res.failureCount > 0) {
        message.warning(
            `成功转化${res.successCount}个线索为客户，${res.failureNames.join(', ')}名称重复，未转化!`
        )
      } else if (res.successCount === 0 && res.failureCount > 0) {
        message.warning(`${res.failureNames.join(', ')}名称重复，未转化!`)
      } else {
        message.info('没有可转化的线索')
      }
      formRef.value.reload()
    }
  } catch {
  }
}

defineExpose({open})
</script>

<style scoped lang="scss">
.detail-body {
  display: flex;
  height: 100%;

  .detail-main {
    width: 550px;

    .detail-main-relation {
      display: flex;
      align-items: center;
      border: 1px solid var(--el-border-color);
      border-radius: 10px;
      margin-bottom: 20px;
      flex-flow: wrap row;
      padding: 5px;
      margin-top: 10px;

      button {
        width: calc(20% - 10px);
        margin: 5px 5px;
        background: #f8f8f8;
        border: 0;
      }
    }

    .detail-main-form {
      border: 1px solid var(--el-border-color);
      padding: 10px;
      border-radius: 10px;
      height: 100%;
    }
  }
}

.detail-dashboard {
  padding-top: 0;
}
</style>
