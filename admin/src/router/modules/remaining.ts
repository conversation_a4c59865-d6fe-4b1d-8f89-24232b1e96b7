import {Layout} from '@/utils/routerHelper'

const {t} = useI18n()
/**
 * redirect: noredirect        当设置 noredirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'          设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
 hidden: true              当设置 true 的时候该路由不会再侧边栏出现 如404，login等页面(默认 false)

 alwaysShow: true          当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式，
 只有一个时，会将那个子路由当做根路由显示在侧边栏，
 若你想不管路由下面的 children 声明的个数都显示你的根路由，
 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，
 一直显示根路由(默认 false)

 title: 'title'            设置该路由在侧边栏和面包屑中展示的名字

 icon: 'svg-name'          设置该路由的图标

 noCache: true             如果设置为true，则不会被 <keep-alive> 缓存(默认 false)

 breadcrumb: false         如果设置为false，则不会在breadcrumb面包屑中显示(默认 true)

 affix: true               如果设置为true，则会一直固定在tag项中(默认 false)

 noTagsView: true          如果设置为true，则不会出现在tag中(默认 false)

 activeMenu: '/dashboard'  显示高亮的路由路径

 followAuth: '/dashboard'  跟随哪个路由进行权限过滤

 canTo: true               设置为true即使hidden为true，也依然可以进行路由跳转(默认 false)
 }
 **/
const remainingRouter: AppRouteRecordRaw[] = [
  {
    path: '/redirect',
    component: Layout,
    name: 'Redirect',
    children: [
      {
        path: '/redirect/:path(.*)',
        name: 'Redirect',
        component: () => import('@/views/Redirect/Redirect.vue'),
        meta: {}
      }
    ],
    meta: {
      hidden: true,
      noTagsView: true
    }
  },
  {
    path: '/',
    redirect: 'home',
    component: () => import('@/views/square/index.vue'),
    meta: {
      hidden: true
    },
    name: 'Index',
    children: [
      {
        path: '',
        redirect: '/home',
        name: 'Root',
        meta: {
          hidden: true
        }
      },
      {
        path: 'home',
        component: () => import('@/views/square/home.vue'),
        name: 'Home',
        meta: {
          hidden: true,
          title: t('router.home'),
          icon: 'ep:home-filled',
          noCache: false,
          affix: true,
          canTo: true
        }
      },
      {
        path: 'coll',
        component: () => import('@/views/square/coll.vue'),
        name: 'Coll',
        meta: {
          hidden: true,
          title: '外呼系统',
          icon: 'ep:home-filled',
          noCache: false,
          affix: true,
          canTo: true
        }
      },
      {
        path: 'Xinclue',
        component: () => import('@/views/square/Xinclue.vue'),
        name: 'Xinclue',
        meta: {
          hidden: true,
          title: '鑫线索',
          icon: 'ep:home-filled',
          noCache: false,
          affix: true,
          canTo: true
        }
      },
      {
        path: 'workmobilephone',
        component: () => import('@/views/square/workmobilephone.vue'),
        name: 'workmobilephone',
        meta: {
          hidden: true,
          title: '鑫淼工作手机',
          icon: 'ep:home-filled',
          noCache: false,
          affix: true,
          canTo: true
        }
      },
      {
        path: 'OAmanagementsystem',
        component: () => import('@/views/square/OAmanagementsystem.vue'),
        name: 'OAmanagementsystem',
        meta: {
          hidden: true,
          title: 'oa管理系统',
          icon: 'ep:home-filled',
          noCache: false,
          affix: true,
          canTo: true
        }
      },
      {
        path: 'CRMManagementSystem',
        component: () => import('@/views/square/CRMManagementSystem.vue'),
        name: 'CRMManagementSystem',
        meta: {
          hidden: true,
          title: 'CRM管理系统',
          icon: 'ep:home-filled',
          noCache: false,
          affix: true,
          canTo: true
        }
      },
      {
        path: 'ERPmanagementsystem',
        component: () => import('@/views/square/ERPmanagementsystem.vue'),
        name: 'ERPmanagementsystem',
        meta: {
          hidden: true,
          title: 'ERP管理系统',
          icon: 'ep:home-filled',
          noCache: false,
          affix: true,
          canTo: true
        }
      },
      {
        path: 'aboutus1',
        component: () => import('@/views/square/aboutUs2.vue'),
        name: 'AboutUs1',
        meta: {
          hidden: true,
          title: '关于我们',
          icon: 'ep:home-filled',
          noCache: false,
          affix: true,
          canTo: true
        }
      },
      {
        path: 'casestudies',
        component: () => import('@/views/square/casestudies.vue'),
        name: 'Casestudies',
        meta: {
          hidden: true,
          title: '案例研究',
          icon: 'ep:home-filled',
          noCache: false,
          affix: true,
          canTo: true
        }
      },
      {
        path: 'Detailedcasestudy',
        component: () => import('@/views/square/Detailedcasestudy.vue'),
        name: 'Detailedcasestudy',
        meta: {
          hidden: true,
          title: '案例研究',
          icon: 'ep:home-filled',
          noCache: false,
          affix: true,
          canTo: true
        }
      },
      {
        path: 'square-list',
        component: () => import('@/views/square/square.vue'),
        name: 'SquareList',
        meta: {
          hidden: true,
          title: '办公广场',
          icon: 'ep:home-filled',
          noTagsView: true
        }
      },
      {
        path: 'square-all-list',
        component: () => import('@/views/square/square-all.vue'),
        name: 'SquareAllList',
        meta: {
          hidden: true,
          title: '办公广场',
          icon: 'ep:home-filled',
          noTagsView: true
        }
      },
      {
        path: 'square-detail',
        component: () => import('@/views/square/application.vue'),
        name: 'SquareDetail',
        meta: {
          hidden: true,
          title: '应用详情',
          icon: 'ep:home-filled',
          noTagsView: true
        }
      },
      {
        path: 'square-price',
        component: () => import('@/views/square/price.vue'),
        name: 'SquarePrice',
        meta: {
          hidden: true,
          title: '定价',
          icon: 'ep:home-filled',
          noTagsView: true
        }
      },
      {
        path: 'about-us',
        component: () => import('@/views/square/aboutUs.vue'),
        name: 'AboutUs',
        meta: {
          hidden: true,
          title: '关于我们',
          icon: 'ep:home-filled',
          noTagsView: true
        }
      },
      {
        path: 'protocol',
        component: () => import('@/views/square/protocol.vue'),
        name: 'Protocol',
        meta: {
          hidden: true,
          title: '使用协议',
          icon: 'ep:home-filled',
          noTagsView: true
        }
      }
    ]
  },
  {
    path: '/admin',
    redirect: '/index',
    name: 'Admin',
    meta: {
      title: '工作台',
      hidden: true,
      icon: 'ep:home-filled',
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/Home/index.vue'),
        name: 'AdminIndex',
        meta: {
          title: '工作台',
          icon: 'ep:home-filled',
          noCache: false,
          affix: true
        }
      }
    ]
  },
  {
    path: '/invite',
    component: () => import('@/views/square/invite.vue'),
    name: 'Invite',
    meta: {
      hidden: true,
      title: '邀请加入',
      icon: 'ep:home-filled',
      noTagsView: true
    }
  },
  {
    path: '/user',
    component: Layout,
    name: 'UserInfo',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'profile',
        component: () => import('@/views/Profile/Index.vue'),
        name: 'Profile',
        meta: {
          canTo: true,
          hidden: true,
          noTagsView: false,
          icon: 'ep:user',
          title: t('common.profile')
        }
      },
      {
        path: 'notify-message',
        component: () => import('@/views/system/notify/my/index.vue'),
        name: 'MyNotifyMessage',
        meta: {
          canTo: true,
          hidden: true,
          noTagsView: false,
          icon: 'ep:message',
          title: '我的站内信'
        }
      },
      {
        path: 'task',
        component: () => import('@/views/system/exportImport/index.vue'),
        name: 'SystemTask',
        meta: {
          canTo: true,
          hidden: true,
          noTagsView: false,
          icon: 'ep:message',
          title: '任务中心'
        }
      }
    ]
  },
  {
    path: '/company',
    component: Layout,
    name: 'CompanyInfo',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'detail',
        component: () => import('@/views/company/index.vue'),
        name: 'CompanyDetail',
        meta: {
          canTo: true,
          hidden: true,
          noTagsView: false,
          icon: 'ep:user',
          title: '企业详情'
        }
      }
    ]
  },
  {
    path: '/dict',
    component: Layout,
    name: 'dict',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'type/data/:dictType',
        component: () => import('@/views/system/dict/data/index.vue'),
        name: 'SystemDictData',
        meta: {
          title: '字典数据',
          noCache: true,
          hidden: true,
          canTo: true,
          icon: '',
          activeMenu: '/system/dict'
        }
      }
    ]
  },

  {
    path: '/codegen',
    component: Layout,
    name: 'CodegenEdit',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'edit',
        component: () => import('@/views/infra/codegen/EditTable.vue'),
        name: 'InfraCodegenEditTable',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          icon: 'ep:edit',
          title: '修改生成配置',
          activeMenu: 'infra/codegen/index'
        }
      }
    ]
  },
  {
    path: '/job',
    component: Layout,
    name: 'JobL',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'job-log',
        component: () => import('@/views/infra/job/logger/index.vue'),
        name: 'InfraJobLog',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          icon: 'ep:edit',
          title: '调度日志',
          activeMenu: 'infra/job/index'
        }
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/Login/Login.vue'),
    name: 'Login',
    meta: {
      hidden: true,
      title: t('router.login'),
      noTagsView: true
    }
  },
  {
    path: '/sso-login',
    component: () => import('@/views/Login/SsoLogin.vue'),
    name: 'SsoLogin',
    meta: {
      hidden: true,
      title: t('router.login'),
      noTagsView: true
    }
  },
  {
    path: '/sso',
    component: () => import('@/views/Login/Login.vue'),
    name: 'SSOLogin',
    meta: {
      hidden: true,
      title: t('router.login'),
      noTagsView: true
    }
  },
  {
    path: '/social-login',
    component: () => import('@/views/Login/SocialLogin.vue'),
    name: 'SocialLogin',
    meta: {
      hidden: true,
      title: t('router.socialLogin'),
      noTagsView: true
    }
  },
  {
    path: '/403',
    component: () => import('@/views/Error/403.vue'),
    name: 'NoAccess',
    meta: {
      hidden: true,
      title: '403',
      noTagsView: true
    }
  },
  {
    path: '/404',
    component: () => import('@/views/Error/404.vue'),
    name: 'NoFound',
    meta: {
      hidden: true,
      title: '404',
      noTagsView: true
    }
  },
  {
    path: '/500',
    component: () => import('@/views/Error/500.vue'),
    name: 'Error',
    meta: {
      hidden: true,
      title: '500',
      noTagsView: true
    }
  },
  {
    path: '/bpm',
    component: Layout,
    name: 'bpm',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'manager/form/edit',
        component: () => import('@/views/bpm/form/editor/index.vue'),
        name: 'BpmFormEditor',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '设计流程表单',
          activeMenu: '/bpm/manager/form'
        }
      },
      {
        path: 'manager/definition',
        component: () => import('@/views/bpm/model/definition/index.vue'),
        name: 'BpmProcessDefinition',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '流程定义',
          activeMenu: '/bpm/manager/model'
        }
      },
      {
        path: 'process-instance/detail',
        component: () => import('@/views/bpm/processInstance/detail/index.vue'),
        name: 'BpmProcessInstanceDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '流程详情',
          activeMenu: '/bpm/task/my'
        },
        props: (route) => ({
          id: route.query.id,
          taskId: route.query.taskId,
          activityId: route.query.activityId
        })
      },
      {
        path: 'process-instance/report',
        component: () => import('@/views/bpm/processInstance/report/index.vue'),
        name: 'BpmProcessInstanceReport',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '数据报表',
          activeMenu: '/bpm/manager/model'
        }
      },
      {
        path: 'oa/leave/create',
        component: () => import('@/views/bpm/oa/leave/create.vue'),
        name: 'OALeaveCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '发起 OA 请假',
          activeMenu: '/bpm/oa/leave'
        }
      },
      {
        path: 'oa/leave/detail',
        component: () => import('@/views/bpm/oa/leave/detail.vue'),
        name: 'OALeaveDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看 OA 请假',
          activeMenu: '/bpm/oa/leave'
        }
      },
      {
        path: 'manager/model/create',
        component: () => import('@/views/bpm/model/form/index.vue'),
        name: 'BpmModelCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '创建流程',
          activeMenu: '/bpm/manager/model'
        }
      },
      {
        path: 'manager/model/:type/:id',
        component: () => import('@/views/bpm/model/form/index.vue'),
        name: 'BpmModelUpdate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '修改流程',
          activeMenu: '/bpm/manager/model'
        }
      },
      {
        path: 'task/create/submit/:id',
        component: () => import('@/views/bpm/processInstance/create/submit.vue'),
        name: 'BpmProcessInstanceSubmit',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '提交流程',
          activeMenu: '/bpm/task/create'
        }
      }
    ]
  },
  {
    path: '/pay',
    component: Layout,
    name: 'pay',
    meta: { hidden: true },
    children: [
      {
        path: 'cashier',
        name: 'PayCashier',
        meta: {
          title: '收银台',
          noCache: true,
          hidden: true
        },
        component: () => import('@/views/pay/cashier/index.vue')
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/Error/404.vue'),
    name: '',
    meta: {
      title: '404',
      hidden: true,
      breadcrumb: false
    }
  },
  {
    path: '/custom/form',
    component: Layout,
    name: 'CustomForm',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'edit/:id(\\d+)',
        name: 'CustomFormEdit',
        component: () => import('@/views/system/customForm/form.vue'),
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '自定义表单',
          activeMenu: '/system/customForm'
        }
      }
    ]
  },
]

export default remainingRouter
